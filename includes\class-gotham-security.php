<?php
/**
 * Gotham Block Security Class
 * Handles all security-related functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Security {
    
    /**
     * Verify nonce for AJAX requests
     */
    public function verify_nonce($nonce, $action = 'gotham_adblock_nonce') {
        return wp_verify_nonce($nonce, $action);
    }
    
    /**
     * Create secure nonce
     */
    public function create_nonce($action = 'gotham_adblock_nonce') {
        return wp_create_nonce($action);
    }
    
    /**
     * Sanitize IP address
     */
    public function sanitize_ip($ip) {
        $ip = sanitize_text_field($ip);
        
        // Validate IP address
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return '';
        }
        
        return $ip;
    }
    
    /**
     * Sanitize user agent
     */
    public function sanitize_user_agent($user_agent) {
        // Remove potentially dangerous characters
        $user_agent = sanitize_text_field($user_agent);
        
        // Limit length to prevent abuse
        if (strlen($user_agent) > 1000) {
            $user_agent = substr($user_agent, 0, 1000);
        }
        
        return $user_agent;
    }
    
    /**
     * Sanitize URL
     */
    public function sanitize_url($url) {
        return esc_url_raw($url);
    }
    
    /**
     * Validate and sanitize screen resolution
     */
    public function sanitize_screen_resolution($resolution) {
        if (!preg_match('/^\d{1,5}x\d{1,5}$/', $resolution)) {
            return '';
        }
        return sanitize_text_field($resolution);
    }
    
    /**
     * Validate timezone
     */
    public function sanitize_timezone($timezone) {
        $timezone = sanitize_text_field($timezone);
        
        // Check if it's a valid timezone
        if (!in_array($timezone, timezone_identifiers_list())) {
            return '';
        }
        
        return $timezone;
    }
    
    /**
     * Rate limiting check
     */
    public function check_rate_limit($ip, $action = 'general', $limit = 60, $window = 3600) {
        $transient_key = 'gotham_rate_limit_' . md5($ip . $action);
        $current_count = get_transient($transient_key);
        
        if (false === $current_count) {
            set_transient($transient_key, 1, $window);
            return true;
        }
        
        if ($current_count >= $limit) {
            return false;
        }
        
        set_transient($transient_key, $current_count + 1, $window);
        return true;
    }
    
    /**
     * Check if user has required capabilities
     */
    public function user_can_manage() {
        return current_user_can('manage_options');
    }
    
    /**
     * Validate event type
     */
    public function validate_event_type($event_type) {
        $allowed_events = array(
            'pop_displayed',
            'popup_closed',
            'button_clicked',
            'adblock_disabled',
            'page_hidden',
            'page_visible'
        );
        
        return in_array($event_type, $allowed_events) ? $event_type : '';
    }
    
    /**
     * Validate status
     */
    public function validate_status($status) {
        $allowed_statuses = array(
            'pending',
            'completed',
            'declined',
            'accepted',
            'tracking',
            'test',
            'verified'
        );
        
        return in_array($status, $allowed_statuses) ? $status : 'pending';
    }
    
    /**
     * Sanitize behavioral data
     */
    public function sanitize_behavioral_data($data) {
        $sanitized = array();
        
        $int_fields = array(
            'mouse_movements',
            'keystrokes',
            'scroll_events',
            'click_events',
            'focus_events',
            'time_on_page',
            'behavior_score',
            'session_duration',
            'plugins_count'
        );
        
        foreach ($int_fields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = max(0, intval($data[$field]));
            }
        }
        
        $text_fields = array(
            'canvas_fingerprint',
            'webgl_vendor',
            'webgl_renderer',
            'webdriver_detected'
        );
        
        foreach ($text_fields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = sanitize_text_field($data[$field]);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Prevent SQL injection by validating table names
     */
    public function validate_table_name($table_name) {
        global $wpdb;
        
        // Only allow our specific table names
        $allowed_tables = array(
            $wpdb->prefix . 'gotham_adblock_stats',
            $wpdb->prefix . 'gotham_bot_stats'
        );
        
        return in_array($table_name, $allowed_tables);
    }
    
    /**
     * Log security events
     */
    public function log_security_event($event, $details = array()) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_entry = array(
                'timestamp' => current_time('mysql'),
                'event' => $event,
                'ip' => $this->get_client_ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'details' => $details
            );
            
            error_log('Gotham Security: ' . wp_json_encode($log_entry));
        }
    }
    
    /**
     * Get real client IP address
     */
    public function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }
    
    /**
     * Check if request is from admin area
     */
    public function is_admin_request() {
        return is_admin() || (defined('DOING_AJAX') && DOING_AJAX);
    }
    
    /**
     * Validate fury mode setting
     */
    public function validate_fury_mode($mode) {
        $valid_modes = array('ssj1', 'ssj2', 'ssj3', 'paused');
        return in_array($mode, $valid_modes) ? $mode : 'ssj1';
    }
    
    /**
     * Validate cookie time setting
     */
    public function validate_cookie_time($time) {
        $valid_times = array('60', '120', '300', '600', '1800', '3600', '7200', '86400', '172800', '604800', '1296000', '2592000');
        return in_array($time, $valid_times) ? $time : '2592000';
    }
    
    /**
     * Validate popup delay
     */
    public function validate_popup_delay($delay) {
        $delay = intval($delay);
        return max(0, min(60, $delay));
    }
}
