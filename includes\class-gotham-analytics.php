<?php
/**
 * Gotham Block Analytics Class
 * Handles analytics tracking and data processing
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Analytics {
    
    private $database;
    private $bot_detector;
    private $security;
    
    public function __construct($database, $bot_detector) {
        $this->database = $database;
        $this->bot_detector = $bot_detector;
        $this->security = new Gotham_Security();
    }
    
    /**
     * Initialize analytics functionality
     */
    public function init() {
        // AJAX handlers for tracking
        add_action('wp_ajax_gotham_track_event', array($this, 'handle_track_event'));
        add_action('wp_ajax_nopriv_gotham_track_event', array($this, 'handle_track_event'));
        
        // Legacy AJAX handler for backward compatibility
        add_action('wp_ajax_gotham_adblock_track_event', array($this, 'handle_track_event'));
        add_action('wp_ajax_nopriv_gotham_adblock_track_event', array($this, 'handle_track_event'));
        
        // Cleanup scheduled task
        add_action('gotham_cleanup_old_data', array($this, 'cleanup_old_data'));
        
        // Enqueue tracking script
        add_action('wp_enqueue_scripts', array($this, 'enqueue_tracking_script'));
    }
    
    /**
     * Enqueue tracking script
     */
    public function enqueue_tracking_script() {
        // Only load on frontend
        if (is_admin()) {
            return;
        }
        
        // Check if plugin is active
        $fury_mode = get_option('gothamadblock_option_fury', 'ssj1');
        if ($fury_mode === 'paused') {
            return;
        }
        
        wp_enqueue_script(
            'gotham-analytics-tracker',
            GOTHAM_BLOCK_PLUGIN_URL . 'assets/analytics-tracker.js',
            array('jquery'),
            GOTHAM_BLOCK_VERSION,
            true
        );
        
        // Localize script with AJAX URL and nonce
        wp_localize_script('gotham-analytics-tracker', 'gothamAnalytics', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => $this->security->create_nonce(),
            'trackingEnabled' => true,
            'debugMode' => defined('WP_DEBUG') && WP_DEBUG
        ));
    }
    
    /**
     * Handle AJAX tracking requests
     */
    public function handle_track_event() {
        // Verify nonce
        $nonce = sanitize_text_field($_POST['nonce'] ?? '');
        if (!$this->security->verify_nonce($nonce)) {
            wp_die('Security check failed', 'Security Error', array('response' => 403));
        }
        
        // Rate limiting
        $ip = $this->security->get_client_ip();
        if (!$this->security->check_rate_limit($ip, 'tracking', 100, 3600)) {
            wp_die('Rate limit exceeded', 'Rate Limit', array('response' => 429));
        }
        
        // Collect and sanitize data
        $data = $this->collect_tracking_data();
        
        // Detect if this is a bot
        $bot_detection = $this->bot_detector->detect_bot(
            $data['user_agent'],
            $data['ip'],
            $data
        );
        
        $data['is_bot'] = $bot_detection['is_bot'] ? 1 : 0;
        $data['bot_confidence'] = $bot_detection['confidence'];
        
        // Store the data
        $result = $this->database->insert_analytics_data($data);
        
        if ($result) {
            wp_send_json_success(array(
                'message' => 'Event tracked successfully',
                'id' => $result,
                'is_bot' => $bot_detection['is_bot']
            ));
        } else {
            wp_send_json_error('Failed to track event');
        }
    }
    
    /**
     * Collect tracking data from request
     */
    private function collect_tracking_data() {
        $data = array();
        
        // Basic request data
        $data['ip'] = $this->security->get_client_ip();
        $data['user_agent'] = $this->security->sanitize_user_agent($_SERVER['HTTP_USER_AGENT'] ?? '');
        $data['url'] = $this->security->sanitize_url($_POST['url'] ?? '');
        $data['referer'] = $this->security->sanitize_url($_POST['referer'] ?? $_SERVER['HTTP_REFERER'] ?? '');
        $data['event_type'] = $this->security->validate_event_type($_POST['event_type'] ?? 'pop_displayed');
        $data['status'] = $this->security->validate_status($_POST['status'] ?? 'pending');
        
        // Browser and system data
        $data['screen_resolution'] = $this->security->sanitize_screen_resolution($_POST['screen_resolution'] ?? '');
        $data['timezone'] = $this->security->sanitize_timezone($_POST['timezone'] ?? '');
        $data['language'] = sanitize_text_field($_POST['language'] ?? '');
        $data['plugins_count'] = max(0, intval($_POST['plugins_count'] ?? 0));
        
        // Fingerprinting data
        $data['canvas_fingerprint'] = sanitize_text_field($_POST['canvas_fingerprint'] ?? '');
        $data['webgl_vendor'] = sanitize_text_field($_POST['webgl_vendor'] ?? '');
        $data['webgl_renderer'] = sanitize_text_field($_POST['webgl_renderer'] ?? '');
        $data['webdriver_detected'] = sanitize_text_field($_POST['webdriver_detected'] ?? 'false');
        
        // Behavioral data
        $data['mouse_movements'] = max(0, intval($_POST['mouse_movements'] ?? 0));
        $data['keystrokes'] = max(0, intval($_POST['keystrokes'] ?? 0));
        $data['scroll_events'] = max(0, intval($_POST['scroll_events'] ?? 0));
        $data['click_events'] = max(0, intval($_POST['click_events'] ?? 0));
        $data['focus_events'] = max(0, intval($_POST['focus_events'] ?? 0));
        $data['time_on_page'] = max(0, intval($_POST['time_on_page'] ?? 0));
        $data['behavior_score'] = max(0, intval($_POST['behavior_score'] ?? 0));
        $data['session_duration'] = max(0, intval($_POST['session_duration'] ?? 0));
        
        return $data;
    }
    
    /**
     * Get analytics dashboard data
     */
    public function get_dashboard_data($filters = array()) {
        // Set default date range if not provided
        if (empty($filters['start_date'])) {
            $filters['start_date'] = date('Y-m-d', strtotime('-30 days'));
        }
        if (empty($filters['end_date'])) {
            $filters['end_date'] = date('Y-m-d');
        }
        
        $data = array();
        
        // Get basic statistics
        $data['total_events'] = $this->database->get_analytics_count($filters);
        
        // Get bot statistics
        $bot_filters = array_merge($filters, array('is_bot' => 1));
        $human_filters = array_merge($filters, array('is_bot' => 0));
        
        $data['bot_events'] = $this->database->get_analytics_count($bot_filters);
        $data['human_events'] = $this->database->get_analytics_count($human_filters);
        
        // Calculate percentages
        if ($data['total_events'] > 0) {
            $data['bot_percentage'] = round(($data['bot_events'] / $data['total_events']) * 100, 2);
            $data['human_percentage'] = round(($data['human_events'] / $data['total_events']) * 100, 2);
        } else {
            $data['bot_percentage'] = 0;
            $data['human_percentage'] = 0;
        }
        
        // Get event type breakdown
        $data['event_breakdown'] = $this->get_event_breakdown($filters);
        
        // Get daily statistics
        $data['daily_stats'] = $this->get_daily_statistics($filters);
        
        // Get top user agents
        $data['top_user_agents'] = $this->get_top_user_agents($filters);
        
        // Get top IPs
        $data['top_ips'] = $this->get_top_ips($filters);
        
        return $data;
    }
    
    /**
     * Get event type breakdown
     */
    private function get_event_breakdown($filters) {
        global $wpdb;
        
        $table_stats = $this->database->get_table_stats();
        $where_clauses = array('1=1');
        $where_values = array();
        
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['end_date'];
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT event_type, COUNT(*) as count FROM {$table_stats} WHERE {$where_sql} GROUP BY event_type ORDER BY count DESC";
        
        if (!empty($where_values)) {
            $prepared_sql = $wpdb->prepare($sql, $where_values);
        } else {
            $prepared_sql = $sql;
        }
        
        return $wpdb->get_results($prepared_sql, ARRAY_A);
    }
    
    /**
     * Get daily statistics
     */
    private function get_daily_statistics($filters) {
        global $wpdb;
        
        $table_stats = $this->database->get_table_stats();
        $where_clauses = array('1=1');
        $where_values = array();
        
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['end_date'];
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT DATE(timestamp) as date, 
                       COUNT(*) as total,
                       SUM(CASE WHEN is_bot = 1 THEN 1 ELSE 0 END) as bots,
                       SUM(CASE WHEN is_bot = 0 THEN 1 ELSE 0 END) as humans
                FROM {$table_stats} 
                WHERE {$where_sql} 
                GROUP BY DATE(timestamp) 
                ORDER BY date ASC";
        
        if (!empty($where_values)) {
            $prepared_sql = $wpdb->prepare($sql, $where_values);
        } else {
            $prepared_sql = $sql;
        }
        
        return $wpdb->get_results($prepared_sql, ARRAY_A);
    }
    
    /**
     * Get top user agents
     */
    private function get_top_user_agents($filters, $limit = 10) {
        global $wpdb;
        
        $table_stats = $this->database->get_table_stats();
        $where_clauses = array('1=1');
        $where_values = array();
        
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['end_date'];
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT user_agent, COUNT(*) as count, 
                       SUM(CASE WHEN is_bot = 1 THEN 1 ELSE 0 END) as bot_count
                FROM {$table_stats} 
                WHERE {$where_sql} AND user_agent != '' 
                GROUP BY user_agent 
                ORDER BY count DESC 
                LIMIT %d";
        
        $where_values[] = $limit;
        $prepared_sql = $wpdb->prepare($sql, $where_values);
        
        return $wpdb->get_results($prepared_sql, ARRAY_A);
    }
    
    /**
     * Get top IPs
     */
    private function get_top_ips($filters, $limit = 10) {
        global $wpdb;
        
        $table_stats = $this->database->get_table_stats();
        $where_clauses = array('1=1');
        $where_values = array();
        
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['end_date'];
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT ip, COUNT(*) as count, 
                       SUM(CASE WHEN is_bot = 1 THEN 1 ELSE 0 END) as bot_count
                FROM {$table_stats} 
                WHERE {$where_sql} AND ip != '' 
                GROUP BY ip 
                ORDER BY count DESC 
                LIMIT %d";
        
        $where_values[] = $limit;
        $prepared_sql = $wpdb->prepare($sql, $where_values);
        
        return $wpdb->get_results($prepared_sql, ARRAY_A);
    }
    
    /**
     * Cleanup old data
     */
    public function cleanup_old_data() {
        $retention_days = apply_filters('gotham_data_retention_days', 90);
        return $this->database->cleanup_old_data($retention_days);
    }
    
    /**
     * Export analytics data
     */
    public function export_data($filters = array(), $format = 'csv') {
        $data = $this->database->get_analytics_data(10000, 0, $filters);
        
        if ($format === 'csv') {
            return $this->export_to_csv($data);
        } elseif ($format === 'json') {
            return wp_json_encode($data);
        }
        
        return false;
    }
    
    /**
     * Export data to CSV format
     */
    private function export_to_csv($data) {
        if (empty($data)) {
            return false;
        }
        
        $output = fopen('php://temp', 'r+');
        
        // Write headers
        fputcsv($output, array_keys($data[0]));
        
        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv_content = stream_get_contents($output);
        fclose($output);
        
        return $csv_content;
    }
    
    /**
     * Get analytics summary for admin dashboard
     */
    public function get_admin_summary() {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $week_ago = date('Y-m-d', strtotime('-7 days'));
        
        return array(
            'today' => $this->database->get_analytics_count(array(
                'start_date' => $today,
                'end_date' => $today
            )),
            'yesterday' => $this->database->get_analytics_count(array(
                'start_date' => $yesterday,
                'end_date' => $yesterday
            )),
            'last_7_days' => $this->database->get_analytics_count(array(
                'start_date' => $week_ago,
                'end_date' => $today
            )),
            'bot_stats' => $this->bot_detector->get_bot_stats()
        );
    }
}
