<?php
/**
 * Gotham Block Admin Class
 * Handles admin interface and settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Admin {
    
    private $analytics;
    private $database;
    private $security;
    
    public function __construct($analytics, $database) {
        $this->analytics = $analytics;
        $this->database = $database;
        $this->security = new Gotham_Security();
        
        $this->init_hooks();
    }
    
    /**
     * Initialize admin hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // AJAX handlers
        add_action('wp_ajax_gotham_get_analytics_data', array($this, 'ajax_get_analytics_data'));
        add_action('wp_ajax_gotham_export_data', array($this, 'ajax_export_data'));
        add_action('wp_ajax_gotham_reset_data', array($this, 'ajax_reset_data'));
        add_action('wp_ajax_gotham_run_bot_detection', array($this, 'ajax_run_bot_detection'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('Gotham Block Settings', 'gotham-block'),
            __('Gotham Block', 'gotham-block'),
            'manage_options',
            'gotham-block-settings',
            array($this, 'render_settings_page')
        );
        
        add_management_page(
            __('Gotham Block Analytics', 'gotham-block'),
            __('Gotham Block Analytics', 'gotham-block'),
            'manage_options',
            'gotham-block-analytics',
            array($this, 'render_analytics_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Main settings group
        register_setting('gotham_block_settings', 'gothamadblock_option_fury', array(
            'sanitize_callback' => array($this->security, 'validate_fury_mode')
        ));
        
        register_setting('gotham_block_settings', 'gothamadblock_option_cookietime', array(
            'sanitize_callback' => array($this->security, 'validate_cookie_time')
        ));
        
        register_setting('gotham_block_settings', 'gothamadblock_option_popup_delay', array(
            'sanitize_callback' => array($this->security, 'validate_popup_delay')
        ));
        
        register_setting('gotham_block_settings', 'gothamadblock_option_powered', array(
            'sanitize_callback' => 'sanitize_text_field'
        ));
        
        register_setting('gotham_block_settings', 'gothamadblock_option_premium_tools', array(
            'sanitize_callback' => 'sanitize_text_field'
        ));
        
        // Add settings sections
        add_settings_section(
            'gotham_block_main_settings',
            __('Main Settings', 'gotham-block'),
            array($this, 'render_main_settings_section'),
            'gotham_block_settings'
        );
        
        add_settings_section(
            'gotham_block_advanced_settings',
            __('Advanced Settings', 'gotham-block'),
            array($this, 'render_advanced_settings_section'),
            'gotham_block_settings'
        );
        
        // Add settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        add_settings_field(
            'gothamadblock_option_fury',
            __('Popup Mode', 'gotham-block'),
            array($this, 'render_fury_field'),
            'gotham_block_settings',
            'gotham_block_main_settings'
        );
        
        add_settings_field(
            'gothamadblock_option_cookietime',
            __('Cookie Duration', 'gotham-block'),
            array($this, 'render_cookie_time_field'),
            'gotham_block_settings',
            'gotham_block_main_settings'
        );
        
        add_settings_field(
            'gothamadblock_option_popup_delay',
            __('Popup Delay (seconds)', 'gotham-block'),
            array($this, 'render_popup_delay_field'),
            'gotham_block_settings',
            'gotham_block_main_settings'
        );
        
        add_settings_field(
            'gothamadblock_option_powered',
            __('Show "Powered by" link', 'gotham-block'),
            array($this, 'render_powered_field'),
            'gotham_block_settings',
            'gotham_block_advanced_settings'
        );
        
        add_settings_field(
            'gothamadblock_option_premium_tools',
            __('Enable Premium Features', 'gotham-block'),
            array($this, 'render_premium_field'),
            'gotham_block_settings',
            'gotham_block_advanced_settings'
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'gotham-block') === false) {
            return;
        }
        
        wp_enqueue_style(
            'gotham-admin-style',
            GOTHAM_BLOCK_PLUGIN_URL . 'assets/admin-style.css',
            array(),
            GOTHAM_BLOCK_VERSION
        );
        
        wp_enqueue_script(
            'gotham-admin-script',
            GOTHAM_BLOCK_PLUGIN_URL . 'assets/admin-script.js',
            array('jquery', 'chart-js'),
            GOTHAM_BLOCK_VERSION,
            true
        );
        
        // Enqueue Chart.js for analytics
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js',
            array(),
            '3.9.1',
            true
        );
        
        wp_localize_script('gotham-admin-script', 'gothamAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => $this->security->create_nonce('gotham_admin_nonce'),
            'strings' => array(
                'confirmReset' => __('Are you sure you want to reset all analytics data? This action cannot be undone.', 'gotham-block'),
                'confirmExport' => __('Export analytics data?', 'gotham-block'),
                'processing' => __('Processing...', 'gotham-block'),
                'error' => __('An error occurred. Please try again.', 'gotham-block')
            )
        ));
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        if (!$this->security->user_can_manage()) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <?php settings_errors(); ?>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('gotham_block_settings');
                do_settings_sections('gotham_block_settings');
                submit_button();
                ?>
            </form>
            
            <div class="gotham-admin-actions">
                <h2><?php _e('Quick Actions', 'gotham-block'); ?></h2>
                
                <p>
                    <button type="button" id="gotham-run-bot-detection" class="button button-secondary">
                        <?php _e('Run Bot Detection on Existing Data', 'gotham-block'); ?>
                    </button>
                    <span class="description"><?php _e('Analyze existing analytics data for bot patterns.', 'gotham-block'); ?></span>
                </p>
                
                <p>
                    <button type="button" id="gotham-reset-data" class="button button-secondary">
                        <?php _e('Reset Analytics Data', 'gotham-block'); ?>
                    </button>
                    <span class="description"><?php _e('Permanently delete all analytics data.', 'gotham-block'); ?></span>
                </p>
            </div>
            
            <div class="gotham-admin-info">
                <h2><?php _e('Plugin Information', 'gotham-block'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Plugin Version', 'gotham-block'); ?></th>
                        <td><?php echo esc_html(GOTHAM_BLOCK_VERSION); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Database Tables', 'gotham-block'); ?></th>
                        <td>
                            <?php if ($this->database->tables_exist()): ?>
                                <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                                <?php _e('Tables exist and are ready', 'gotham-block'); ?>
                            <?php else: ?>
                                <span class="dashicons dashicons-warning" style="color: orange;"></span>
                                <?php _e('Tables missing - please deactivate and reactivate the plugin', 'gotham-block'); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Premium Status', 'gotham-block'); ?></th>
                        <td>
                            <?php if (defined('KINGBOO') && KINGBOO): ?>
                                <span class="dashicons dashicons-star-filled" style="color: gold;"></span>
                                <?php _e('Premium features active', 'gotham-block'); ?>
                            <?php else: ?>
                                <span class="dashicons dashicons-star-empty"></span>
                                <?php _e('Free version', 'gotham-block'); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        if (!$this->security->user_can_manage()) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        $summary = $this->analytics->get_admin_summary();
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="gotham-analytics-summary">
                <div class="gotham-stat-boxes">
                    <div class="gotham-stat-box">
                        <h3><?php _e('Today', 'gotham-block'); ?></h3>
                        <div class="gotham-stat-number"><?php echo esc_html($summary['today']); ?></div>
                    </div>
                    
                    <div class="gotham-stat-box">
                        <h3><?php _e('Yesterday', 'gotham-block'); ?></h3>
                        <div class="gotham-stat-number"><?php echo esc_html($summary['yesterday']); ?></div>
                    </div>
                    
                    <div class="gotham-stat-box">
                        <h3><?php _e('Last 7 Days', 'gotham-block'); ?></h3>
                        <div class="gotham-stat-number"><?php echo esc_html($summary['last_7_days']); ?></div>
                    </div>
                    
                    <div class="gotham-stat-box">
                        <h3><?php _e('Bot Detection', 'gotham-block'); ?></h3>
                        <div class="gotham-stat-number"><?php echo esc_html($summary['bot_stats']['bot_percentage']); ?>%</div>
                        <div class="gotham-stat-label"><?php _e('Bots detected', 'gotham-block'); ?></div>
                    </div>
                </div>
            </div>
            
            <div class="gotham-analytics-filters">
                <h2><?php _e('Analytics Dashboard', 'gotham-block'); ?></h2>
                
                <form id="gotham-analytics-filters" method="get">
                    <input type="hidden" name="page" value="gotham-block-analytics">
                    
                    <label for="start_date"><?php _e('Start Date:', 'gotham-block'); ?></label>
                    <input type="date" id="start_date" name="start_date" value="<?php echo esc_attr($_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'))); ?>">
                    
                    <label for="end_date"><?php _e('End Date:', 'gotham-block'); ?></label>
                    <input type="date" id="end_date" name="end_date" value="<?php echo esc_attr($_GET['end_date'] ?? date('Y-m-d')); ?>">
                    
                    <button type="submit" class="button button-primary"><?php _e('Update', 'gotham-block'); ?></button>
                    <button type="button" id="gotham-export-data" class="button button-secondary"><?php _e('Export CSV', 'gotham-block'); ?></button>
                </form>
            </div>
            
            <div class="gotham-analytics-charts">
                <div class="gotham-chart-container">
                    <canvas id="gotham-daily-chart"></canvas>
                </div>
                
                <div class="gotham-chart-container">
                    <canvas id="gotham-events-chart"></canvas>
                </div>
            </div>
            
            <div id="gotham-analytics-data" style="display: none;">
                <!-- Analytics data will be loaded here via AJAX -->
            </div>
        </div>
        <?php
    }
    
    /**
     * Render settings sections
     */
    public function render_main_settings_section() {
        echo '<p>' . __('Configure the main popup behavior and appearance.', 'gotham-block') . '</p>';
    }
    
    public function render_advanced_settings_section() {
        echo '<p>' . __('Advanced configuration options.', 'gotham-block') . '</p>';
    }
    
    /**
     * Render settings fields
     */
    public function render_fury_field() {
        $value = get_option('gothamadblock_option_fury', 'ssj1');
        $options = array(
            'ssj1' => __('Gentle (SSJ1) - Polite request', 'gotham-block'),
            'ssj2' => __('Moderate (SSJ2) - More persuasive', 'gotham-block'),
            'ssj3' => __('Aggressive (SSJ3) - Blocks content', 'gotham-block'),
            'paused' => __('Paused - Disable popup', 'gotham-block')
        );
        
        echo '<select name="gothamadblock_option_fury" id="gothamadblock_option_fury">';
        foreach ($options as $key => $label) {
            printf(
                '<option value="%s" %s>%s</option>',
                esc_attr($key),
                selected($value, $key, false),
                esc_html($label)
            );
        }
        echo '</select>';
        echo '<p class="description">' . __('Choose how aggressively to display the ad blocker message.', 'gotham-block') . '</p>';
    }
    
    public function render_cookie_time_field() {
        $value = get_option('gothamadblock_option_cookietime', '2592000');
        $options = array(
            '60' => __('1 minute', 'gotham-block'),
            '300' => __('5 minutes', 'gotham-block'),
            '1800' => __('30 minutes', 'gotham-block'),
            '3600' => __('1 hour', 'gotham-block'),
            '86400' => __('1 day', 'gotham-block'),
            '604800' => __('1 week', 'gotham-block'),
            '2592000' => __('30 days', 'gotham-block')
        );
        
        echo '<select name="gothamadblock_option_cookietime" id="gothamadblock_option_cookietime">';
        foreach ($options as $key => $label) {
            printf(
                '<option value="%s" %s>%s</option>',
                esc_attr($key),
                selected($value, $key, false),
                esc_html($label)
            );
        }
        echo '</select>';
        echo '<p class="description">' . __('How long to remember that a user has seen the popup.', 'gotham-block') . '</p>';
    }
    
    public function render_popup_delay_field() {
        $value = get_option('gothamadblock_option_popup_delay', '0');
        echo '<input type="number" name="gothamadblock_option_popup_delay" id="gothamadblock_option_popup_delay" value="' . esc_attr($value) . '" min="0" max="60" />';
        echo '<p class="description">' . __('Delay in seconds before showing the popup (0-60).', 'gotham-block') . '</p>';
    }
    
    public function render_powered_field() {
        $value = get_option('gothamadblock_option_powered', 'non');
        echo '<label><input type="radio" name="gothamadblock_option_powered" value="oui" ' . checked($value, 'oui', false) . ' /> ' . __('Yes', 'gotham-block') . '</label><br>';
        echo '<label><input type="radio" name="gothamadblock_option_powered" value="non" ' . checked($value, 'non', false) . ' /> ' . __('No', 'gotham-block') . '</label>';
        echo '<p class="description">' . __('Show a small "Powered by Gotham Block" link in the popup.', 'gotham-block') . '</p>';
    }
    
    public function render_premium_field() {
        $value = get_option('gothamadblock_option_premium_tools', 'non');
        echo '<label><input type="radio" name="gothamadblock_option_premium_tools" value="oui" ' . checked($value, 'oui', false) . ' /> ' . __('Yes', 'gotham-block') . '</label><br>';
        echo '<label><input type="radio" name="gothamadblock_option_premium_tools" value="non" ' . checked($value, 'non', false) . ' /> ' . __('No', 'gotham-block') . '</label>';
        echo '<p class="description">' . __('Enable premium features (requires valid license).', 'gotham-block') . '</p>';
    }
    
    /**
     * AJAX handlers
     */
    public function ajax_get_analytics_data() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'gotham_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!$this->security->user_can_manage()) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $filters = array(
            'start_date' => sanitize_text_field($_POST['start_date'] ?? ''),
            'end_date' => sanitize_text_field($_POST['end_date'] ?? '')
        );
        
        $data = $this->analytics->get_dashboard_data($filters);
        wp_send_json_success($data);
    }
    
    public function ajax_export_data() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'gotham_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!$this->security->user_can_manage()) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $filters = array(
            'start_date' => sanitize_text_field($_POST['start_date'] ?? ''),
            'end_date' => sanitize_text_field($_POST['end_date'] ?? '')
        );
        
        $csv_data = $this->analytics->export_data($filters, 'csv');
        
        if ($csv_data) {
            $filename = 'gotham-analytics-' . date('Y-m-d') . '.csv';
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($csv_data));
            
            echo $csv_data;
            exit;
        }
        
        wp_send_json_error('Export failed');
    }
    
    public function ajax_reset_data() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'gotham_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!$this->security->user_can_manage()) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $result = $this->database->reset_analytics_data();
        
        if ($result) {
            wp_send_json_success('Analytics data reset successfully');
        } else {
            wp_send_json_error('Failed to reset data');
        }
    }
    
    public function ajax_run_bot_detection() {
        if (!$this->security->verify_nonce($_POST['nonce'] ?? '', 'gotham_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!$this->security->user_can_manage()) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $bot_detector = new Gotham_Bot_Detector();
        $result = $bot_detector->batch_detect_bots(500, 0);
        
        wp_send_json_success($result);
    }
}
