<?php
/**
 * @package Gotham Block Extra Light
 * @version 1.5.1
 */
/*
Plugin Name: Gotham Block Extra Light
Description: 🇬🇧 ULTRA Light plugin to inform your visitors that ad blockers are killing the viability of your site, and invite them to deactivate them 🇫🇷 Plugin ULTRA Léger pour informer tout simplement vos visiteurs que les bloqueurs de publicité tuent la viabilité de votre site, et les invite à les désactiver.
Version: 1.5.1
Author: Kapsule Network
Author URI: https://www.kapsulecorp.com/
License: GPLv2
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GOTHAM_BLOCK_VERSION', '1.5.1');
define('GOTHAM_BLOCK_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GOTHAM_BLOCK_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GOTHAM_BLOCK_PLUGIN_FILE', __FILE__);

// Legacy constant for backward compatibility
define('GOTHAMBKOCKADBLOCK_ROOTPATH', GOTHAM_BLOCK_PLUGIN_DIR);

// Load core classes
require_once GOTHAM_BLOCK_PLUGIN_DIR . 'includes/class-gotham-security.php';
require_once GOTHAM_BLOCK_PLUGIN_DIR . 'includes/class-gotham-database.php';
require_once GOTHAM_BLOCK_PLUGIN_DIR . 'includes/class-gotham-bot-detector.php';
require_once GOTHAM_BLOCK_PLUGIN_DIR . 'includes/class-gotham-analytics.php';
require_once GOTHAM_BLOCK_PLUGIN_DIR . 'includes/class-gotham-popup.php';

// Initialize main plugin class
class Gotham_Block_Plugin {

    private static $instance = null;
    private $security;
    private $database;
    private $bot_detector;
    private $analytics;
    private $popup;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_components();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(GOTHAM_BLOCK_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(GOTHAM_BLOCK_PLUGIN_FILE, array($this, 'deactivate'));
        add_action('init', array($this, 'init'));
        add_action('admin_init', array($this, 'admin_init'));
    }

    /**
     * Initialize plugin components
     */
    private function init_components() {
        $this->security = new Gotham_Security();
        $this->database = new Gotham_Database();
        $this->bot_detector = new Gotham_Bot_Detector();
        $this->analytics = new Gotham_Analytics($this->database, $this->bot_detector);
        $this->popup = new Gotham_Popup($this->analytics);
    }

    /**
     * Plugin activation
     */
    public function activate() {
        $this->database->create_tables();
        $this->database->migrate_if_needed();

        // Set default options
        $this->set_default_options();

        // Clear any existing caches
        wp_cache_flush();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up scheduled events
        wp_clear_scheduled_hook('gotham_cleanup_old_data');
        wp_cache_flush();
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for internationalization
        load_plugin_textdomain('gotham-block', false, dirname(plugin_basename(GOTHAM_BLOCK_PLUGIN_FILE)) . '/languages');

        // Initialize components
        $this->popup->init();
        $this->analytics->init();

        // Schedule cleanup task
        if (!wp_next_scheduled('gotham_cleanup_old_data')) {
            wp_schedule_event(time(), 'daily', 'gotham_cleanup_old_data');
        }
    }

    /**
     * Admin initialization
     */
    public function admin_init() {
        if (is_admin()) {
            $this->init_admin();
        }
    }

    /**
     * Initialize admin interface
     */
    private function init_admin() {
        require_once GOTHAM_BLOCK_PLUGIN_DIR . 'admin/class-gotham-admin.php';
        new Gotham_Admin($this->analytics, $this->database);
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            'gothamadblock_option_fury' => 'ssj1',
            'gothamadblock_option_cookietime' => '2592000', // 30 days
            'gothamadblock_option_popup_delay' => '0',
            'gothamadblock_option_powered' => 'non',
            'gothamadblock_option_premium_tools' => 'non'
        );

        foreach ($defaults as $option => $value) {
            if (false === get_option($option)) {
                add_option($option, $value);
            }
        }
    }

    /**
     * Get component instances
     */
    public function get_security() { return $this->security; }
    public function get_database() { return $this->database; }
    public function get_bot_detector() { return $this->bot_detector; }
    public function get_analytics() { return $this->analytics; }
    public function get_popup() { return $this->popup; }
}

// Premium functionality check (simplified and secure)
function gotham_check_premium_status() {
    $premium_enabled = get_option('gothamadblock_option_premium_tools', 'non');

    if ($premium_enabled === 'oui') {
        // Load premium validation
        $premium_file = GOTHAM_BLOCK_PLUGIN_DIR . 'premium/valid_api.php';
        if (file_exists($premium_file)) {
            require_once $premium_file;
            $status = check_kapsuleapi_ghostpremium_licence();
            define('KINGBOO', ($status === 'FREETRIAL' || $status === 'VALID'));
        } else {
            define('KINGBOO', false);
        }
    } else {
        define('KINGBOO', false);
    }
}

// Initialize premium check
gotham_check_premium_status();

// Initialize the plugin
function gotham_block_init() {
    Gotham_Block_Plugin::get_instance();
}
add_action('plugins_loaded', 'gotham_block_init');

// Legacy compatibility - keep old popup logic for now but mark as deprecated
// TODO: Remove this in future version once new system is fully tested
$gothamadblock_option_fury = get_option('gothamadblock_option_fury');

// Only load legacy code if new system is not available
if (!class_exists('Gotham_Block_Plugin') && ($gothamadblock_option_fury != "paused") && (!is_admin())) {
	
	// Est-on en mode FURY ou non ?
	if (($gothamadblock_option_fury == "ssj2") OR ($gothamadblock_option_fury == "ssj3")) { $agressive_mode = true;} else {$agressive_mode = false;}
	//////////////////////////////

	if ((!isset($_COOKIE['gothamadblock_last_visit_time'])) OR ($agressive_mode == true)) { // Si Pas de Cookie ou Si Mode Agressif activé on affiche la Popup

	// Enqueue analytics tracker script
	function gothamadblock_register_analytics_scripts() {
		wp_enqueue_script('jquery'); // Ensure jQuery is loaded
		wp_register_script( 'gotham-analytics-tracker', plugins_url( '/analytics-tracker.js', __FILE__ ), array('jquery'), '1.5.0', true );
		wp_enqueue_script( 'gotham-analytics-tracker' );

		// Localize script with AJAX URL
		wp_localize_script( 'gotham-analytics-tracker', 'gotham_ajax', array(
			'ajaxurl' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce('gotham_adblock_nonce')
		) );
	}
	add_action( 'wp_enqueue_scripts', 'gothamadblock_register_analytics_scripts', 0 );
		
		// Popup si Honeypot bloqué par un Adblocker
		function gothamadblock_mapop() {
			$defopiks = plugin_dir_url( __FILE__ ).'stop.png';
			$igotit = plugin_dir_url( __FILE__ ).'ok.png';
			$nonce = wp_create_nonce('gotham_adblock_nonce');
			
			// Track popup display
			gotham_adblock_track_event('pop_displayed', 'pending');

			echo '<script>
			// Make nonce available globally for analytics tracker
			window.gotham_adblock_nonce = "' . $nonce . '";

			function gothamadblock_myClosePop() {
				var mes = document.getElementById("gothamadblock_msg");
				var over = document.getElementById("gothamadblock_overlayh_n");
				mes.style.display = "none";
				over.style.display = "none";
				document.body.classList.remove("gtmab_leviator");

				// Track that user closed without disabling adblock
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: {
						action: "gotham_adblock_track_event",
						event_type: "popup_closed",
						status: "declined",
						nonce: "' . $nonce . '"
					}
				});
			}

			function gothamadblock_myClosePopSSJ() {
				// Collect behavioral and fingerprint data
				var behaviorData = window.gothamBehaviorTracker ? window.gothamBehaviorTracker.getData() : {};
				var fingerprintData = window.gothamFingerprint ? window.gothamFingerprint.getData() : {};

				// Track that user acknowledged the message (button clicked)
				jQuery.ajax({
					url: ajaxurl,
					type: "POST",
					data: Object.assign({
						action: "gotham_adblock_track_event",
						event_type: "button_clicked",
						status: "acknowledged",
						nonce: "' . $nonce . '"
					}, behaviorData, fingerprintData),
					success: function() {
						// Set up verification check after page reload
						sessionStorage.setItem("gotham_check_adblock_disabled", "true");
						window.location.reload();
					}
				});
			}
			</script>';


			//////////// Si pas de message personnalisé

							$gothamadblock_option_messageperso_title = get_option('gothamadblock_option_messageperso_title');
							if ($gothamadblock_option_messageperso_title == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												
												$popup_title = "Adblock détecté";
												
											} else {
												
												$popup_title = "Adblock Detected";
												
											}

										//////////// !Zone de Test Langage
							} else {
								
								$popup_title = $gothamadblock_option_messageperso_title;
							
							}

							$gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso');
							if ($gothamadblock_option_messageperso == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$mon_texte_sensibilisation = "<p><u>Ce site internet ne peut exister que grâce à la présence de publicité</u>.<br />Merci de <strong>couper votre logiciel Adblock</Strong> sur ce site et de cliquer sur le bouton j'ai compris.</p>";
											}
											else {
												$mon_texte_sensibilisation = "<p><u>This website can only exist thanks to the presence of advertising</u>.<br />Please <strong>deactivate your Adblock</Strong> software on this site and click on the button I understood.</p>";
											}

										//////////// !Zone de Test Langage
							}
							else {
							$mon_texte_sensibilisation = $gothamadblock_option_messageperso;
							$mon_texte_sensibilisation = wpautop($mon_texte_sensibilisation); // Conversion des sauts de ligne pour corriger le bug du saut de ligne
							}

							$gothamadblock_option_messageperso_button = get_option('gothamadblock_option_messageperso_button');
							if ($gothamadblock_option_messageperso_button == "") {
										//////////// Zone de Test Langage
											$lang   = '';
											if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
												$popup_ctatext = "J'ai compris";
											}
											else {

												$popup_ctatext = "I Understood";
											}
										//////////// !Zone de Test Langage
							}
							else {
							$popup_ctatext = $gothamadblock_option_messageperso_button;
							}

			// Est-on en SSJ3 ? Si oui la popup recharge la page et donc revérifie si le adblock est bien coupé, sinon cela coupe la popup.
			$gothamadblock_option_fury = get_option('gothamadblock_option_fury');
			if ($gothamadblock_option_fury == "ssj3") {$janemba="gothamadblock_myClosePopSSJ()";} else {$janemba="gothamadblock_myClosePop()";}
			
			// Construction de la Popup
			$cestbononatout_onconstruit = "<div id='gothamadblock_msg' style='display:block;'><h2>$popup_title</h2><img src='$defopiks' alt='Oing' height='300' width='300' />$mon_texte_sensibilisation<button id='gtab_mehn' onclick='$janemba'>$popup_ctatext</button></div><div id='gothamadblock_overlayh_n' style='display:block;'></div>"; 
			$cestbononatout_onconstruit = str_replace(array("\n", "\r\n", "\r", "\t", "    "), "", $cestbononatout_onconstruit); // On vire tous les sauts de ligne

			
			 // Si bloqué on affiche la popup en JS
			 /* Mécanisme inspiré d'un script de AdGlare Ad Server */
			echo "
			<script>
				function gothamBatAdblock() {
					var a = document.createElement('div');
					a.innerHTML = '&nbsp;';
					a.className = 'gothamads publicite 300x250 text-ad text_ad text_ads text-ads pub_728x90 textAd text-ad-links adsbox moneytizer';
					a.style = 'position: absolute !important; width: 0!important; height: 1px !important; left: -1000px !important; top: -10000px !important;';
					var r = false;
					try {
						document.body.appendChild(a);
						var e = document.getElementsByClassName('gothamads')[0];
						if(e.offsetHeight === 0 || e.clientHeight === 0) r = true;
						if(window.getComputedStyle !== undefined) {
							var tmp = window.getComputedStyle(e, null);
							if(tmp && (tmp.getPropertyValue('display') == 'none' || tmp.getPropertyValue('visibility') == 'hidden')) r = true;
						}
						document.body.removeChild(a);
					} catch (e) {}
					return r;
				}
		   if(gothamBatAdblock()) {
		   	// Get popup delay setting from WordPress
		   	var popupDelay = " . intval(get_option('gothamadblock_option_popup_delay', 0)) . " * 1000; // Convert to milliseconds

		   	function showGothamPopup() {
		   		// Create popup elements dynamically
		   		var popupHTML = \"$cestbononatout_onconstruit\";
		   		var tempDiv = document.createElement('div');
		   		tempDiv.innerHTML = popupHTML;

		   		// Add popup elements to body
		   		while (tempDiv.firstChild) {
		   			document.body.appendChild(tempDiv.firstChild);
		   		}

		   		document.body.classList.add('gtmab_leviator');
		   	}

		   	if (popupDelay > 0) {
		   		// Delay popup display
		   		setTimeout(function() {
		   			// Re-check adblock status before showing popup (user might have disabled it)
		   			if(gothamBatAdblock()) {
		   				showGothamPopup();
		   			}
		   		}, popupDelay);
		   	} else {
		   		// Show popup immediately
		   		showGothamPopup();
		   	}
		  } else {
		  	// Adblock is NOT detected - check if we should track a conversion
		  	if (sessionStorage.getItem('gotham_check_adblock_disabled') === 'true') {
		  		// User previously clicked button and now adblock is disabled - track conversion!
		  		sessionStorage.removeItem('gotham_check_adblock_disabled');

		  		// Send verified conversion event with behavioral data
		  		if (typeof jQuery !== 'undefined') {
		  			var behaviorData = window.gothamBehaviorTracker ? window.gothamBehaviorTracker.getData() : {};
		  			var fingerprintData = window.gothamFingerprint ? window.gothamFingerprint.getData() : {};

		  			jQuery.ajax({
		  				url: '" . admin_url('admin-ajax.php') . "',
		  				type: 'POST',
		  				data: Object.assign({
		  					action: 'gotham_adblock_track_event',
		  					event_type: 'adblock_disabled',
		  					status: 'verified',
		  					nonce: '" . wp_create_nonce('gotham_adblock_nonce') . "'
		  				}, behaviorData, fingerprintData),
		  				success: function(response) {
		  					console.log('Gotham Analytics: Verified conversion tracked successfully');
		  				}
		  			});
		  		}
		  	}
		  }

		  // Initialize behavioral tracking and fingerprinting
		  window.gothamBehaviorTracker = {
		  	mouseMovements: 0,
		  	clickTiming: [],
		  	scrollEvents: 0,
		  	interactionDepth: 0,
		  	sessionStart: Date.now(),

		  	init: function() {
		  		var self = this;

		  		// Track mouse movements
		  		document.addEventListener('mousemove', function() {
		  			self.mouseMovements++;
		  		});

		  		// Track clicks with timing
		  		document.addEventListener('click', function() {
		  			var now = Date.now();
		  			if (self.clickTiming.length > 0) {
		  				var timeDiff = now - self.clickTiming[self.clickTiming.length - 1];
		  				if (timeDiff < 1000) { // Only track rapid clicks
		  					self.clickTiming.push(timeDiff);
		  				}
		  			}
		  			self.clickTiming.push(now);
		  			self.interactionDepth++;
		  		});

		  		// Track scroll behavior
		  		document.addEventListener('scroll', function() {
		  			self.scrollEvents++;
		  		});
		  	},

		  	getData: function() {
		  		var avgClickTiming = 0;
		  		if (this.clickTiming.length > 1) {
		  			var timings = [];
		  			for (var i = 1; i < this.clickTiming.length; i++) {
		  				timings.push(this.clickTiming[i] - this.clickTiming[i-1]);
		  			}
		  			avgClickTiming = timings.reduce(function(a, b) { return a + b; }, 0) / timings.length;
		  		}

		  		var behaviorScore = 0;
		  		if (this.mouseMovements > 5) behaviorScore += 2;
		  		if (this.scrollEvents > 2) behaviorScore += 2;
		  		if (this.interactionDepth > 1) behaviorScore += 2;
		  		if (avgClickTiming > 100) behaviorScore += 2;

		  		return {
		  			behavior_score: behaviorScore,
		  			mouse_movements: this.mouseMovements,
		  			click_timing: Math.round(avgClickTiming),
		  			scroll_behavior: this.scrollEvents,
		  			interaction_depth: this.interactionDepth,
		  			session_duration: Date.now() - this.sessionStart
		  		};
		  	}
		  };

		  window.gothamFingerprint = {
		  	getData: function() {
		  		var data = {};

		  		// Check for WebDriver
		  		data.webdriver_detected = (navigator.webdriver === true ||
		  			window.navigator.webdriver === true ||
		  			window.callPhantom !== undefined ||
		  			window._phantom !== undefined) ? 'true' : 'false';

		  		// Canvas fingerprinting
		  		try {
		  			var canvas = document.createElement('canvas');
		  			var ctx = canvas.getContext('2d');
		  			ctx.textBaseline = 'top';
		  			ctx.font = '14px Arial';
		  			ctx.fillText('Gotham fingerprint test', 2, 2);
		  			data.canvas_fingerprint = canvas.toDataURL().substring(0, 50);
		  		} catch (e) {
		  			data.canvas_fingerprint = 'blocked';
		  		}

		  		// WebGL information
		  		try {
		  			var gl = document.createElement('canvas').getContext('webgl');
		  			if (gl) {
		  				var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
		  				if (debugInfo) {
		  					data.webgl_vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
		  					data.webgl_renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
		  				}
		  			}
		  		} catch (e) {
		  			data.webgl_vendor = 'unavailable';
		  			data.webgl_renderer = 'unavailable';
		  		}

		  		// Screen resolution
		  		data.screen_resolution = screen.width + 'x' + screen.height;

		  		// Timezone
		  		data.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown';

		  		// Plugin count
		  		data.plugins_count = navigator.plugins ? navigator.plugins.length : 0;

		  		return data;
		  	}
		  };

		  // Initialize tracking
		  if (document.readyState === 'loading') {
		  	document.addEventListener('DOMContentLoaded', function() {
		  		window.gothamBehaviorTracker.init();
		  	});
		  } else {
		  	window.gothamBehaviorTracker.init();
		  }
			</script>";

			  // Si bloqué on affiche le CSS
			echo "<style type='text/css'>
				.gtmab_leviator {height:100%;overflow:hidden;}
				#gothamadblock_msg{position:fixed;width:800px;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:40px 0!important;}#gothamadblock_msg img{width:150px;height:150px;margin:20px auto!important;clear:both}#gothamadblock_msg h2{font-weight:700!important;font-family:arial!important;padding:10px 0!important;font-size:26px!important;}#gothamadblock_msg p{margin:30px 0!important;}button#gtab_mehn {cursor:pointer;display: inline-block;text-align: center;vertical-align: middle;padding: 12px 24px;border: 1px solid #4443cf;border-radius: 8px;background: #807eff;background: -webkit-gradient(linear, left top, left bottom, from(#807eff), to(#4443cf));background: -moz-linear-gradient(top, #807eff, #4443cf);background: linear-gradient(to bottom, #807eff, #4443cf);font: normal normal bold 20px arial;color: #ffffff;text-decoration: none;}button#gtab_mehn:focus,button#gtab_mehn:hover{border:1px solid ##504ff4;background:#9a97ff;background:-webkit-gradient(linear,left top,left bottom,from(#9a97ff),to(#5250f8));background:-moz-linear-gradient(top,#9a97ff,#5250f8);background:linear-gradient(to bottom,#9a97ff,#5250f8);color:#fff;text-decoration:none}button#gtab_mehn:active{background:#4443cf;background:-webkit-gradient(linear,left top,left bottom,from(#4443cf),to(#4443cf));background:-moz-linear-gradient(top,#4443cf,#4443cf);background:linear-gradient(to bottom,#4443cf,#4443cf)}button#gtab_mehn:before{content:'';display:inline-block;height:24px;width:24px;line-height:24px;margin:0 4px -6px -4px;position:relative;top:0;left:-3px;background:url($igotit) no-repeat left center transparent;background-size:100% 100%}#gothamadblock_overlayh_n{position:fixed;width:100%;margin:0 auto;opacity:.8;background:#000;height:100%;display:block;float:left;z-index:99999998;top:0;}
				@media only screen and (max-width: 1024px){#gothamadblock_msg{position:fixed;width:90%;margin:0 auto;background:#fff;height:auto;display:block;float:left;z-index:99999999;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%);border-radius:8px;border:4px solid orange;padding:10px;}}@media only screen and (max-width: 767px){#gothamadblock_msg img {width:100px;height:100px;}#gothamadblock_msg {padding:10px!important;}}
				</style>";
			}

			// On lance le plugin
			function gothamadblock_gothamkill() {
				$chosen = gothamadblock_mapop();
				}
			add_action( 'wp_footer', 'gothamadblock_gothamkill' );
	}


	// On dépose un cookie 
	add_action( 'init', 'gothamadblock_add_Cookie' );
	function gothamadblock_add_Cookie() {
		if (!isset($_COOKIE['gothamadblock_last_visit_time'])) { // Si un cookie n'est pas déjà en place et le délai afférant entrain de courir
		$tempsdecuisson = get_option('gothamadblock_option_cookietime'); // On récupère la durée voulue
		if ((empty($tempsdecuisson)) OR ($tempsdecuisson=="")) {$tempsdecuisson=2592000;} // Si paramétrage de la durée de vie du cookie vide, on fixe à 30 jours par défaut
		setcookie("gothamadblock_last_visit_time", "1", time()+$tempsdecuisson, "/"); // On pose le cookie
		}
	}
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Copyrighting
//////////////////////////////////////////////////////////////////////////////////////
$gothamadblock_option_powered_check = get_option('gothamadblock_option_powered');
if ($gothamadblock_option_powered_check == "oui") {
		function gothamadblock_powered_seo() {
			echo "<p style='text-align:center;'>Plugin <a href='https://www.kapsulecorp.com/' target='_blank' rel='noopener'>Kapsule Corp</a></p>";
			}
		add_action( 'wp_footer', 'gothamadblock_powered_seo' );
}

//////////////////////////////////////////////////////////////////////////////////////
// Création du Menu et de l'enregistrement des options
//////////////////////////////////////////////////////////////////////////////////////

// Si c'est l'admin
if ( is_admin() ){

	///////////////////////////////////
	// On créé les options dans le SQL
	///////////////////////////////////

	function gotham_blockadblock_html_sanitize_callback ($string)
	{
		$gotham_blockadblock_allowed_html = array(
		'a' => array(
			'href' => array(),
			'title' => array(),
			'rel' => array(),
			'target' => array()
		),
		'br' => array(),
		'em' => array(),
		'strong' => array(),
		'b' => array(),
		'u' => array(),
		'strike' => array(),
		'h1' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h2' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h3' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h4' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'h5' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'p' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'span' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ul' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'ol' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		),
		'li' => array(
			'id' => array(),
			'class' => array(),
			'style' => array()
		)
		);	
		
		return wp_kses($string,$gotham_blockadblock_allowed_html);
	}

	add_action( 'admin_init', 'gothamadblock_batarang' );
	function gothamadblock_batarang() {
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_fury', 'gotham_sanitize_fury_mode' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_cookietime', 'gotham_sanitize_cookie_time' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_popup_delay', 'gotham_sanitize_popup_delay' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_title', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso', 'gotham_blockadblock_html_sanitize_callback' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_messageperso_button', 'sanitize_text_field' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_powered', 'gotham_sanitize_yes_no' );
		// PREMIUM API KEY
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_premium_tools', 'gotham_sanitize_yes_no' );
		register_setting( 'gothamadblockbat-settings-group', 'gothamadblock_option_apijeton', 'sanitize_text_field' );
		add_action('admin_enqueue_scripts', 'check_licence_update_ghostpremium');
	}

	// Enhanced sanitization functions
	function gotham_sanitize_fury_mode($input) {
	    $valid_modes = array('ssj1', 'ssj2', 'ssj3', 'paused');
	    return in_array($input, $valid_modes) ? $input : 'ssj1';
	}

	function gotham_sanitize_cookie_time($input) {
	    $valid_times = array('60', '120', '300', '600', '1800', '3600', '7200', '86400', '172800', '604800', '1296000', '2592000');
	    return in_array($input, $valid_times) ? $input : '2592000';
	}

	function gotham_sanitize_popup_delay($input) {
	    $delay = intval($input);
	    // Ensure delay is between 0 and 60 seconds
	    if ($delay < 0) return 0;
	    if ($delay > 60) return 60;
	    return $delay;
	}

	function gotham_sanitize_yes_no($input) {
	    return ($input === 'oui' || $input === 'yes') ? 'oui' : 'non';
	}

	///////////////////////////////////
	// On créé le menu
	//////////////////////////////////

	add_action('admin_menu','gothamadblock_setupmenu');
	function gothamadblock_setupmenu(){
		  add_menu_page(
		      'Configuration de Gotham Block Adblock',
		      'G BlockAdblock',
		      'manage_options', // Changed from 'administrator' to 'manage_options' for better security
		      'gotham-plugin',
		      'gothamadblock_init_cave',
		      'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMjQgMjQiIGlkPSJMYXllcl8xIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PHBhdGggZD0iTTE2LjUsMTYuNSAgYy03LTMtOSwzLTksM2MtNS41LTItNyw0LTcsNGMwLTkuNSw0LTEzLDQtMTNzLTEsMywyLDNzMS45OTk4Njg0LTQuNSwwLTVoMC4yOTI4OTM5ICBjMC40NTI3NTI2LDAsMC44ODY5NjE1LTAuMTc5ODU1MywxLjIwNzEwNTYtMC40OTk5OTlMOC4wMDAwMDEsNy45OTk5OTk1QzguMzIwMTQ0Nyw3LjY3OTg1NTMsOC41LDcuMjQ1NjQ2NSw4LjUsNi43OTI4OTM5VjYuNSAgYzAuNSwxLjk5OTg2ODQsNSwzLDUsMHMtMy0yLTMtMnMzLjUtNCwxMy00YzAsMC02LDEuNS00LDdDMTkuNSw3LjUsMTMuNSw5LjUsMTYuNSwxNi41IiBmaWxsPSJub25lIiBzdHJva2U9IiMzMDNDNDIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIi8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PGcvPjxnLz48Zy8+PC9zdmc+'
		  );

		  // Add Analytics submenu
		  add_submenu_page(
		      'gotham-plugin',
		      'Analytics',
		      'Analytics',
		      'manage_options',
		      'gotham-analytics',
		      array(new Gotham_Analytics_Admin(), 'dashboard_page')
		  );

		  // Add Data Management submenu
		  add_submenu_page(
		      'gotham-plugin',
		      'Data Management',
		      'Data Management',
		      'manage_options',
		      'gotham-data-management',
		      'gotham_data_management_page'
		  );
	}

	///////////////////////////////////
	// On charge le JS de l'admin
	//////////////////////////////////

	function gothamadblock_monjsdansladmin($hook) {
		// Only load on our admin page
		if ($hook !== 'toplevel_page_gotham-plugin') {
			return;
		}

		// Enqueue modern admin styles and scripts
		wp_enqueue_style('gotham-admin-css', plugins_url('/assets/gotham-admin.css', __FILE__), array(), '2.0.0');
		wp_enqueue_script('gotham-admin-js', plugins_url('/assets/gotham-admin.js', __FILE__), array('jquery'), '2.0.0', true);

		// Add notification styles
		echo "<style>
		/* Notification Styles */
		.gotham-notification {
			padding: 15px 20px;
			border-radius: 8px;
			margin: 20px 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			opacity: 0;
			transform: translateY(-10px);
			transition: all 0.3s ease;
		}

		.gotham-notification.visible {
			opacity: 1;
			transform: translateY(0);
		}

		.gotham-notification-info {
			background: #d1ecf1;
			border: 1px solid #bee5eb;
			color: #0c5460;
		}

		.gotham-notification-warning {
			background: #fff3cd;
			border: 1px solid #ffeaa7;
			color: #856404;
		}

		.gotham-notification-error {
			background: #f8d7da;
			border: 1px solid #f5c6cb;
			color: #721c24;
		}

		.gotham-notification-neutral {
			background: #e2e3e5;
			border: 1px solid #d6d8db;
			color: #383d41;
		}

		.notification-close {
			background: none;
			border: none;
			font-size: 20px;
			cursor: pointer;
			padding: 0;
			margin-left: 15px;
			opacity: 0.7;
		}

		.notification-close:hover {
			opacity: 1;
		}

		/* Field Error Styles */
		.gotham-form-control.error {
			border-color: #dc3545;
			box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
		}

		.field-error {
			color: #dc3545;
			font-size: 0.85em;
			margin-top: 5px;
		}

		/* Tooltip Styles */
		.gotham-tooltip {
			position: absolute;
			background: #333;
			color: white;
			padding: 8px 12px;
			border-radius: 4px;
			font-size: 0.85em;
			z-index: 1000;
			opacity: 0;
			transition: opacity 0.2s ease;
			pointer-events: none;
		}

		.gotham-tooltip.visible {
			opacity: 1;
		}

		.gotham-tooltip::after {
			content: '';
			position: absolute;
			top: 100%;
			left: 50%;
			margin-left: -5px;
			border: 5px solid transparent;
			border-top-color: #333;
		}
		</style>";
	}

	add_action('admin_enqueue_scripts', 'gothamadblock_monjsdansladmin');

	// Création de la page d'options du plugin ////////////////
	function gothamadblock_init_cave(){
	    // Security check - ensure user has proper capabilities
	    if (!current_user_can('manage_options')) {
	        wp_die(__('You do not have sufficient permissions to access this page.'));
	    }

	///////////////////////////////////////
	/// Mini zone de langage pour l'admin
	///////////////////////////////////////

	///FRANCAIS
	if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) {
		$txt_adwin_welcome = " Bienvenue dans la BatBase ";
		$txt_adwin_yes = "Oui";
		$txt_adwin_no = "Non";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Titre personnalisé";
		$txt_adwin_corpus = "Texte personnalisé (HTML autorisé)";
		$txt_adwin_cta = "Bouton personnalisé";
		$txt_adwin_firemode = "1. Je choisis le degré d'agressivité du plugin !";
		$txt_adwin_firemode_p = "<p>Dans tous les cas, la popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invitera à désactiver son adblocker. Mais vous pouvez choisir ci-dessous le comportement de la popup en fixant son degré d'agressivité selon 3 niveaux.</p><ul><li> SSJ1 (Choix par défaut) : Le message ne s'affichera qu'une fois tous les x minutes/heures/jours (30 jours par défaut), tant que l'internaute aura un logiciel Adblock activé, mais l'internaute pourra fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ2 : Le message s'affichera à chaque chargement de page, tant que l'internaute aura un logiciel Adblock activé, cependant l'internaute pourra à chaque fois fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)</li><li> SSJ3 : Le message s'affichera à chaque chargement de page, et l'internaute ne pourra pas naviguer sur votre site tant qu'il aura un logiciel Adblock activé. Rends la navigation impossible avec Adblock !</li></ul>";
		$txt_adwin_mecha = "2. Je customise (ou pas) la popup !";
		$txt_adwin_mecha_p = "Saisissez le texte de votre choix ou laissez vide pour afficher le texte par défaut.";
		$txt_adwin_mot_jours = "jours";
		$txt_adwin_blokright_title = "À propos de ce plugin";
		$txt_adwin_blokright_corpus_1 = "Gotham Block Extra Light est un plugin léger pour informer vos visiteurs que les bloqueurs de publicité affectent la viabilité de votre site.";
		$txt_adwin_blokright_corpus_2 = "Voir sur WordPress.org";
		$txt_adwin_blokright_corpus_3 = "Support et aide";
		$txt_adwin_blokright_aime = "Vous aimez ce plugin ?";
		$txt_adwin_blokright_vote = "Donnez-nous une note 5 étoiles";
		$txt_adwin_blokright_sur = "sur";
	}
	///ANGLAIS
	else {
		$txt_adwin_welcome = "Welcome to the BatBase ";
		$txt_adwin_yes = "Yes";
		$txt_adwin_no = "No";
		$txt_adwin_ssj1 = "SSJ1 Light";
		$txt_adwin_ssj2 = "SSJ2 Agressive";
		$txt_adwin_ssj3 = "SSJ3 Fury";
		$txt_adwin_paused = "Pause ";
		$txt_adwin_titre = "Customized Headline";
		$txt_adwin_corpus = "Customized message (HTML allowed)";
		$txt_adwin_cta = "Customized Button";
		$txt_adwin_firemode = "1. I choose the degree of aggressiveness of the plugin";
		$txt_adwin_firemode_p = "<p>In any case, the popup will inform the user that blocking ads kills your buisness model and will invite him to deactivate his adblocker. But you can choose below the behavior of the popup by setting its degree of aggressiveness according to 3 levels.</p><ul><li> SSJ1 (Default) : The message will only be displayed once every X minute/hours/days (30 days by default), as long as the user has Adblock software activated, but the user can close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ2 : The message will appear on each page load, as long as the user has Adblock software activated, however the user can each time close the popup and continue browsing your site normally (even with Adblock activated)</li><li> SSJ3 : The message will appear on each page load, and the user will not be able to navigate on your site as long as he has Adblock software activated. This level of aggressiveness makes navigation impossible with Adblock!</li></ul>";
		$txt_adwin_mecha = "2. I customize (or not) the popup";
		$txt_adwin_mecha_p = "Enter your text or leave blank for display the default text";
		$txt_adwin_mot_jours = "days";
		$txt_adwin_blokright_title = "About this plugin";
		$txt_adwin_blokright_corpus_1 = "Gotham Block Extra Light is a lightweight plugin to inform your visitors that ad blockers affect your site's viability.";
		$txt_adwin_blokright_corpus_2 = "View on WordPress.org";
		$txt_adwin_blokright_corpus_3 = "Support and help";
		$txt_adwin_blokright_aime = "Do you like this plugin?";
		$txt_adwin_blokright_vote = "Give us a 5-star rating";
		$txt_adwin_blokright_sur = "on";
	}
	////////////////////////////////////////

	?>
	<div class="gotham_ad_wrap">
	  <h1 id="logo_admin"><?php echo $txt_adwin_welcome; ?></h1>

	  <div class="gotham_ad_form">
	  <form method="post" action="options.php">
	  <?php settings_fields( 'gothamadblockbat-settings-group' ); ?>
	  <?php do_settings_sections('gothamadblockbat-settings-group'); ?>


		  <!-- Detection Mode Configuration Card -->
		  <div class="gotham-config-card">
			<div class="gotham-card-header">
				<h3><span class="card-icon">⚡</span><?php echo $txt_adwin_firemode; ?></h3>
			</div>
			<div class="gotham-card-body">
				<div class="gotham-card-description">
					<?php echo $txt_adwin_firemode_p; ?>
				</div>

				<!-- Hidden select for form submission -->
				<?php $gothamadblock_option_fury = get_option('gothamadblock_option_fury'); ?>
				<select id="gothamadblock_option_fury" name="gothamadblock_option_fury" style="display: none;">
					<option value="ssj1" <?php selected( $gothamadblock_option_fury, 'ssj1' ); ?>><?php echo $txt_adwin_ssj1; ?></option>
					<option value="ssj2" <?php selected( $gothamadblock_option_fury, 'ssj2' ); ?>><?php echo $txt_adwin_ssj2; ?></option>
					<option value="ssj3" <?php selected( $gothamadblock_option_fury, 'ssj3' ); ?>><?php echo $txt_adwin_ssj3; ?></option>
					<option value="paused" <?php selected( $gothamadblock_option_fury, 'paused' ); ?>><?php echo $txt_adwin_paused; ?></option>
				</select>

				<!-- Modern Mode Selection Cards -->
				<div class="detection-modes">
					<div class="mode-card ssj1 <?php echo ($gothamadblock_option_fury == 'ssj1') ? 'selected' : ''; ?>" data-mode="ssj1">
						<div class="mode-header">
							<div class="mode-icon">SSJ1</div>
							<h4 class="mode-title"><?php echo $txt_adwin_ssj1; ?></h4>
						</div>
						<div class="mode-description">
							<?php if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ): ?>
								Mode léger qui affiche le message une fois par période configurée. L'utilisateur peut fermer la popup et continuer à naviguer.
							<?php else: ?>
								Light mode that shows the message once per configured period. Users can close the popup and continue browsing.
							<?php endif; ?>
						</div>
						<ul class="mode-features">
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Respectueux de l\'utilisateur' : 'User-friendly approach'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Fréquence configurable' : 'Configurable frequency'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Navigation possible' : 'Browsing allowed'; ?></li>
						</ul>
					</div>

					<div class="mode-card ssj2 <?php echo ($gothamadblock_option_fury == 'ssj2') ? 'selected' : ''; ?>" data-mode="ssj2">
						<div class="mode-header">
							<div class="mode-icon">SSJ2</div>
							<h4 class="mode-title"><?php echo $txt_adwin_ssj2; ?></h4>
						</div>
						<div class="mode-description">
							<?php if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ): ?>
								Mode agressif qui affiche le message à chaque chargement de page, mais permet toujours de fermer la popup.
							<?php else: ?>
								Aggressive mode that shows the message on every page load, but still allows closing the popup.
							<?php endif; ?>
						</div>
						<ul class="mode-features">
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Plus insistant' : 'More persistent'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Chaque page' : 'Every page load'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Popup fermable' : 'Closeable popup'; ?></li>
						</ul>
					</div>

					<div class="mode-card ssj3 <?php echo ($gothamadblock_option_fury == 'ssj3') ? 'selected' : ''; ?>" data-mode="ssj3">
						<div class="mode-header">
							<div class="mode-icon">SSJ3</div>
							<h4 class="mode-title"><?php echo $txt_adwin_ssj3; ?></h4>
						</div>
						<div class="mode-description">
							<?php if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ): ?>
								Mode furieux qui bloque complètement la navigation tant que l'adblock est activé. À utiliser avec précaution !
							<?php else: ?>
								Fury mode that completely blocks browsing until adblock is disabled. Use with caution!
							<?php endif; ?>
						</div>
						<ul class="mode-features">
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Très efficace' : 'Highly effective'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Navigation bloquée' : 'Browsing blocked'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Conversion forcée' : 'Forced conversion'; ?></li>
						</ul>
					</div>

					<div class="mode-card paused <?php echo ($gothamadblock_option_fury == 'paused') ? 'selected' : ''; ?>" data-mode="paused">
						<div class="mode-header">
							<div class="mode-icon">⏸</div>
							<h4 class="mode-title"><?php echo $txt_adwin_paused; ?></h4>
						</div>
						<div class="mode-description">
							<?php if ( 'fr_' === substr( get_user_locale(), 0, 3 ) ): ?>
								Désactive complètement la détection d'adblock. Aucune popup ne sera affichée.
							<?php else: ?>
								Completely disables adblock detection. No popups will be shown to users.
							<?php endif; ?>
						</div>
						<ul class="mode-features">
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Plugin désactivé' : 'Plugin disabled'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Aucune popup' : 'No popups'; ?></li>
							<li><?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Navigation normale' : 'Normal browsing'; ?></li>
						</ul>
					</div>
				</div>

				<!-- Cookie Time Selector for SSJ1 Mode -->
				<?php $gothamadblock_option_cookietime = get_option('gothamadblock_option_cookietime'); ?>
				<div class="cookie-time-selector" <?php if ($gothamadblock_option_fury != "ssj1") { ?>style="display:none;"<?php } ?>>
					<label for="gothamadblock_option_cookietime">
						<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Fréquence d\'affichage pour le mode SSJ1:' : 'Display frequency for SSJ1 mode:'; ?>
					</label>
					<select id="gothamadblock_option_cookietime" name="gothamadblock_option_cookietime" class="gotham-form-control">
						<option value="2592000" <?php selected( $gothamadblock_option_cookietime, '2592000' ); ?>>30 <?php echo $txt_adwin_mot_jours; ?> (Default)</option>
						<option value="1296000" <?php selected( $gothamadblock_option_cookietime, '1296000' ); ?>>15 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="604800" <?php selected( $gothamadblock_option_cookietime, '604800' ); ?>>7 <?php echo $txt_adwin_mot_jours; ?></option>
						<option value="172800" <?php selected( $gothamadblock_option_cookietime, '172800' ); ?>>48H</option>
						<option value="86400" <?php selected( $gothamadblock_option_cookietime, '86400' ); ?>>24H</option>
						<option value="7200" <?php selected( $gothamadblock_option_cookietime, '7200' ); ?>>2H</option>
						<option value="3600" <?php selected( $gothamadblock_option_cookietime, '3600' ); ?>>1H</option>
						<option value="1800" <?php selected( $gothamadblock_option_cookietime, '1800' ); ?>>30 min</option>
						<option value="600" <?php selected( $gothamadblock_option_cookietime, '600' ); ?>>10 min</option>
						<option value="300" <?php selected( $gothamadblock_option_cookietime, '300' ); ?>>5 min</option>
						<option value="120" <?php selected( $gothamadblock_option_cookietime, '120' ); ?>>2 min</option>
						<option value="60" <?php selected( $gothamadblock_option_cookietime, '60' ); ?>>1 min</option>
					</select>
				</div>

				<!-- Popup Display Delay Setting -->
				<?php $gothamadblock_option_popup_delay = get_option('gothamadblock_option_popup_delay', 0); ?>
				<div class="popup-delay-selector">
					<label for="gothamadblock_option_popup_delay">
						<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Délai d\'affichage de la popup:' : 'Popup Display Delay:'; ?>
					</label>
					<div class="gotham-form-group inline">
						<input type="number"
							   id="gothamadblock_option_popup_delay"
							   name="gothamadblock_option_popup_delay"
							   class="gotham-form-control small"
							   value="<?php echo esc_attr($gothamadblock_option_popup_delay); ?>"
							   min="0"
							   max="60"
							   step="1" />
						<span class="input-suffix">
							<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'secondes' : 'seconds'; ?>
						</span>
					</div>
					<div class="setting-description">
						<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ?
							'Nombre de secondes à attendre avant d\'afficher la popup après le chargement de la page (0-60 secondes). 0 = affichage immédiat.' :
							'Number of seconds to wait before showing the popup after page load (0-60 seconds). 0 = immediate display.'; ?>
					</div>
				</div>
			</div>
		  </div>

		  <!-- Popup Customization Card -->
		  <div class="gotham-config-card">
			<div class="gotham-card-header">
				<h3><span class="card-icon">🎨</span><?php echo $txt_adwin_mecha; ?></h3>
			</div>
			<div class="gotham-card-body">
				<div class="gotham-card-description">
					<?php echo $txt_adwin_mecha_p; ?>
				</div>

				<div class="gotham-form-group">
					<label for="gothamadblock_option_messageperso_title" class="gotham-form-label">
						<?php echo $txt_adwin_titre; ?>:
					</label>
					<input type="text"
						   id="gothamadblock_option_messageperso_title"
						   name="gothamadblock_option_messageperso_title"
						   class="gotham-form-control"
						   value="<?php echo esc_attr(get_option('gothamadblock_option_messageperso_title')); ?>"
						   placeholder="<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Titre personnalisé de la popup...' : 'Custom popup title...'; ?>" />
				</div>

				<div class="gotham-form-group">
					<label for="gothamadblock_option_messageperso" class="gotham-form-label">
						<?php echo $txt_adwin_corpus; ?>:
					</label>
					<?php $gothamadblock_option_messageperso = get_option('gothamadblock_option_messageperso'); ?>
					<textarea id="gothamadblock_option_messageperso"
							  name="gothamadblock_option_messageperso"
							  class="gotham-form-control large"
							  placeholder="<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Message personnalisé de la popup (HTML autorisé)...' : 'Custom popup message (HTML allowed)...'; ?>"><?php echo esc_textarea($gothamadblock_option_messageperso); ?></textarea>
				</div>

				<div class="gotham-form-group">
					<label for="gothamadblock_option_messageperso_button" class="gotham-form-label">
						<?php echo $txt_adwin_cta; ?>:
					</label>
					<input type="text"
						   id="gothamadblock_option_messageperso_button"
						   name="gothamadblock_option_messageperso_button"
						   class="gotham-form-control"
						   value="<?php echo esc_attr(get_option('gothamadblock_option_messageperso_button')); ?>"
						   placeholder="<?php echo ( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Texte du bouton personnalisé...' : 'Custom button text...'; ?>" />
				</div>
			</div>
		  </div>

		  <!-- Submit Section -->
		  <div class="gotham-submit-section">
			<?php submit_button(
				( 'fr_' === substr( get_user_locale(), 0, 3 ) ) ? 'Enregistrer les paramètres' : 'Save Settings',
				'primary gotham-submit-btn',
				'submit',
				false
			); ?>
		  </div>
	  </form>
	  </div>
	   <div class="gotham_ad_credit">
						<h3>Gotham Adblock</h3>
						<div class="inside">
							<h4 class="inner"><?php echo $txt_adwin_blokright_title; ?></h4>
							<p class="inner"><?php echo $txt_adwin_blokright_corpus_1; ?></p>
							<ul>
								<li>- <a href="https://wordpress.org/plugins/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_2; ?></a></li>
								<li>- <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/"><?php echo $txt_adwin_blokright_corpus_3; ?></a></li>
							</ul>
							<hr>
							<h4 class="inner"> <?php echo $txt_adwin_blokright_aime; ?></h4>
							<p class="inner"> <a href="https://wordpress.org/support/plugin/gotham-block-extra-light/reviews/?filter=5#new-post" target="_blank"><?php echo $txt_adwin_blokright_vote; ?></a> <?php echo $txt_adwin_blokright_sur; ?> WordPress.org</p>
							<hr>
							<p class="inner"> Copyright <a href="https://www.kapsulecorp.com/">Kapsule Corp</a></p>
						</div>
		</div>
	</div>
	
	<?php
	
	} // Fin du Init Cave

	// Data Management Page
	function gotham_data_management_page() {
	    // Security check - ensure user has proper capabilities
	    if (!current_user_can('manage_options')) {
	        wp_die(__('You do not have sufficient permissions to access this page.'));
	    }

	    // Handle form submissions
	    if (isset($_POST['gotham_reset_data']) && wp_verify_nonce($_POST['gotham_reset_nonce'], 'gotham_reset_data_action')) {
	        $reset_result = gotham_reset_analytics_data();
	        if ($reset_result['success']) {
	            echo '<div class="notice notice-success"><p>' . $reset_result['message'] . '</p></div>';
	        } else {
	            echo '<div class="notice notice-error"><p>' . $reset_result['message'] . '</p></div>';
	        }
	    }



	    // Auto-ensure bot detection columns exist and apply retroactive detection
	    gotham_ensure_bot_detection_columns();

	    // Auto-apply bot detection to existing data if needed
	    gotham_auto_apply_bot_detection();

	    // Get current data statistics
	    global $wpdb;
	    $main_table = $wpdb->prefix . 'gotham_adblock_stats';
	    $bot_table = $wpdb->prefix . 'gotham_bot_stats';

	    $main_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$main_table'") == $main_table;
	    $bot_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$bot_table'") == $bot_table;

	    // Check if is_bot column exists
	    $has_bot_column = false;
	    if ($main_table_exists) {
	        $columns = $wpdb->get_results("SHOW COLUMNS FROM $main_table");
	        foreach ($columns as $column) {
	            if ($column->Field === 'is_bot') {
	                $has_bot_column = true;
	                break;
	            }
	        }
	    }

	    $stats = [
	        'main_records' => $main_table_exists ? $wpdb->get_var("SELECT COUNT(*) FROM $main_table") : 0,
	        'bot_records' => 0,
	        'human_records' => 0,
	        'unique_human_ips' => 0,
	        'unique_bot_ips' => 0,
	        'oldest_record' => null,
	        'newest_record' => null
	    ];

	    if ($main_table_exists && $stats['main_records'] > 0) {
	        if ($has_bot_column) {
	            // Count records by bot status
	            $stats['human_records'] = $wpdb->get_var("SELECT COUNT(*) FROM $main_table WHERE is_bot = 0");
	            $stats['bot_records'] = $wpdb->get_var("SELECT COUNT(*) FROM $main_table WHERE is_bot = 1");

	            // Count unique IPs (matching Analytics dashboard methodology)
	            $stats['unique_human_ips'] = $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $main_table WHERE is_bot = 0");
	            $stats['unique_bot_ips'] = $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $main_table WHERE is_bot = 1");
	        } else {
	            // No bot detection - assume all human
	            $stats['human_records'] = $stats['main_records'];
	            $stats['unique_human_ips'] = $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $main_table");
	        }

	        // Add bot table records if it exists
	        if ($bot_table_exists) {
	            $bot_table_records = $wpdb->get_var("SELECT COUNT(*) FROM $bot_table");
	            $stats['bot_records'] += $bot_table_records;
	        }

	        $stats['oldest_record'] = $wpdb->get_var("SELECT MIN(created_at) FROM $main_table");
	        $stats['newest_record'] = $wpdb->get_var("SELECT MAX(created_at) FROM $main_table");
	    }

	    ?>
	    <div class="wrap">
	        <h1>🗂️ Gotham Block - Data Management</h1>

	        <div class="gotham-data-management-container">
	            <!-- Current Data Statistics -->
	            <div class="gotham-config-card">
	                <div class="gotham-card-header">
	                    <h3><span class="card-icon">📊</span>Current Analytics Data</h3>
	                </div>
	                <div class="gotham-card-body">
	                    <div class="data-stats-grid">
	                        <div class="stat-item">
	                            <div class="stat-value"><?php echo number_format($stats['main_records']); ?></div>
	                            <div class="stat-label">Total Records</div>
	                            <div class="stat-description">All database entries</div>
	                        </div>
	                        <div class="stat-item human">
	                            <div class="stat-value"><?php echo number_format($stats['unique_human_ips']); ?></div>
	                            <div class="stat-label">Unique Human Users</div>
	                            <div class="stat-description"><?php echo number_format($stats['human_records']); ?> total records</div>
	                        </div>
	                        <div class="stat-item bot">
	                            <div class="stat-value"><?php echo number_format($stats['unique_bot_ips']); ?></div>
	                            <div class="stat-label">Unique Bot IPs</div>
	                            <div class="stat-description"><?php echo number_format($stats['bot_records']); ?> total records</div>
	                        </div>
	                        <div class="stat-item quality">
	                            <div class="stat-value"><?php echo $stats['main_records'] > 0 ? round(($stats['unique_human_ips'] / ($stats['unique_human_ips'] + $stats['unique_bot_ips'])) * 100, 1) : 0; ?>%</div>
	                            <div class="stat-label">Data Quality</div>
	                            <div class="stat-description">Human traffic percentage</div>
	                        </div>
	                    </div>

	                    <?php if ($stats['oldest_record']): ?>
	                    <div class="data-timeline">
	                        <p><strong>Data Collection Period:</strong></p>
	                        <p>From: <?php echo date('F j, Y g:i A', strtotime($stats['oldest_record'])); ?></p>
	                        <p>To: <?php echo date('F j, Y g:i A', strtotime($stats['newest_record'])); ?></p>
	                    </div>
	                    <?php endif; ?>
	                </div>
	            </div>

	            <!-- Bot Detection Status -->
	            <div class="gotham-config-card">
	                <div class="gotham-card-header">
	                    <h3><span class="card-icon">🤖</span>Automatic Bot Detection</h3>
	                </div>
	                <div class="gotham-card-body">
	                    <div class="info-message">
	                        <p><strong>✅ Bot Detection is Active:</strong> The system automatically detects and filters bot traffic in real-time.</p>
	                        <p><strong>Features:</strong></p>
	                        <ul>
	                            <li>🔍 Real-time bot detection for all new traffic</li>
	                            <li>🧠 Multi-layer analysis (user-agent, headers, behavior, fingerprinting)</li>
	                            <li>📊 Automatic data quality filtering in analytics</li>
	                            <li>⚡ Background processing of existing data</li>
	                            <li>🎯 Human-only conversion tracking for accurate metrics</li>
	                        </ul>
	                        <?php if ($has_bot_column): ?>
	                        <p><strong>Status:</strong> <span style="color: #28a745; font-weight: bold;">✅ Fully Operational</span></p>
	                        <p><em>Bot detection data is automatically displayed in the Analytics Dashboard.</em></p>
	                        <?php else: ?>
	                        <p><strong>Status:</strong> <span style="color: #ffc107; font-weight: bold;">⚠️ Setting Up</span></p>
	                        <p><em>Bot detection columns are being added to the database...</em></p>
	                        <?php endif; ?>
	                    </div>
	                </div>
	            </div>

	            <!-- Data Reset Section -->
	            <div class="gotham-config-card danger-zone">
	                <div class="gotham-card-header">
	                    <h3><span class="card-icon">⚠️</span>Danger Zone - Data Reset</h3>
	                </div>
	                <div class="gotham-card-body">
	                    <div class="warning-message">
	                        <p><strong>⚠️ Warning:</strong> This action will permanently delete ALL analytics data from your database. This cannot be undone!</p>
	                        <p>This will remove:</p>
	                        <ul>
	                            <li>All user interaction records</li>
	                            <li>All conversion tracking data</li>
	                            <li>All bot detection records</li>
	                            <li>All geographic and browser analytics</li>
	                            <li>All historical data and trends</li>
	                        </ul>
	                    </div>

	                    <form method="post" id="reset-form" style="margin-top: 20px;">
	                        <?php wp_nonce_field('gotham_reset_data_action', 'gotham_reset_nonce'); ?>

	                        <div class="confirmation-section">
	                            <label class="confirmation-checkbox">
	                                <input type="checkbox" id="confirm-reset" required>
	                                <span class="checkmark"></span>
	                                I understand that this action cannot be undone and will delete all analytics data
	                            </label>
	                        </div>

	                        <button type="submit"
	                                name="gotham_reset_data"
	                                class="button button-danger"
	                                id="reset-button"
	                                disabled
	                                onclick="return confirmReset();">
	                            🗑️ Delete All Analytics Data
	                        </button>
	                    </form>
	                </div>
	            </div>
	        </div>
	    </div>

	    <style>
	    .gotham-data-management-container {
	        max-width: 800px;
	        margin: 20px 0;
	    }

	    .gotham-config-card {
	        background: white;
	        border: 1px solid #ccd0d4;
	        border-radius: 8px;
	        margin-bottom: 20px;
	        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	    }

	    .gotham-config-card.danger-zone {
	        border-color: #dc3545;
	        background: #fff5f5;
	    }

	    .gotham-card-header {
	        padding: 15px 20px;
	        border-bottom: 1px solid #eee;
	        background: #f8f9fa;
	        border-radius: 8px 8px 0 0;
	    }

	    .danger-zone .gotham-card-header {
	        background: #fee;
	        border-bottom-color: #fcc;
	    }

	    .gotham-card-header h3 {
	        margin: 0;
	        font-size: 1.2em;
	        color: #2c3e50;
	    }

	    .card-icon {
	        margin-right: 8px;
	    }

	    .gotham-card-body {
	        padding: 20px;
	    }

	    .data-stats-grid {
	        display: grid;
	        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	        gap: 20px;
	        margin-bottom: 20px;
	    }

	    .stat-item {
	        text-align: center;
	        padding: 15px;
	        background: #f8f9fa;
	        border-radius: 8px;
	        border: 2px solid #e9ecef;
	    }

	    .stat-item.human {
	        background: #d4edda;
	        border-color: #28a745;
	    }

	    .stat-item.bot {
	        background: #fff3cd;
	        border-color: #ffc107;
	    }

	    .stat-item.quality {
	        background: #e3f2fd;
	        border-color: #2196f3;
	    }

	    .stat-value {
	        font-size: 2em;
	        font-weight: bold;
	        color: #2c3e50;
	        margin-bottom: 5px;
	    }

	    .stat-label {
	        font-size: 0.9em;
	        color: #666;
	        text-transform: uppercase;
	        letter-spacing: 0.5px;
	        margin-bottom: 3px;
	    }

	    .stat-description {
	        font-size: 0.75em;
	        color: #888;
	        font-style: italic;
	    }

	    .data-timeline {
	        background: #e3f2fd;
	        padding: 15px;
	        border-radius: 6px;
	        border-left: 4px solid #2196f3;
	    }

	    .warning-message {
	        background: #fff3cd;
	        border: 1px solid #ffeaa7;
	        border-radius: 6px;
	        padding: 15px;
	        margin-bottom: 20px;
	    }

	    .info-message {
	        background: #e3f2fd;
	        border: 1px solid #90caf9;
	        border-radius: 6px;
	        padding: 15px;
	        margin-bottom: 20px;
	    }

	    .warning-message ul {
	        margin: 10px 0 0 20px;
	    }

	    .confirmation-section {
	        margin: 20px 0;
	    }

	    .confirmation-checkbox {
	        display: flex;
	        align-items: center;
	        cursor: pointer;
	        font-weight: 500;
	    }

	    .confirmation-checkbox input[type="checkbox"] {
	        margin-right: 10px;
	        transform: scale(1.2);
	    }

	    .button-danger {
	        background: #dc3545 !important;
	        border-color: #dc3545 !important;
	        color: white !important;
	        font-weight: 600;
	        padding: 10px 20px;
	        font-size: 14px;
	    }

	    .button-danger:hover:not(:disabled) {
	        background: #c82333 !important;
	        border-color: #bd2130 !important;
	    }

	    .button-danger:disabled {
	        background: #6c757d !important;
	        border-color: #6c757d !important;
	        cursor: not-allowed;
	        opacity: 0.6;
	    }
	    </style>

	    <script>
	    document.getElementById('confirm-reset').addEventListener('change', function() {
	        document.getElementById('reset-button').disabled = !this.checked;
	    });

	    function confirmReset() {
	        return confirm('⚠️ FINAL WARNING ⚠️\n\nYou are about to permanently delete ALL analytics data.\n\nThis includes:\n- All user interaction records\n- All conversion data\n- All bot detection records\n- All historical analytics\n\nThis action CANNOT be undone!\n\nAre you absolutely sure you want to proceed?');
	    }
	    </script>
	    <?php
	}

	// Data Reset Function - DEPRECATED: Use new class-based system
	function gotham_reset_analytics_data() {
	    // Security check
	    if (!current_user_can('manage_options')) {
	        return [
	            'success' => false,
	            'message' => 'Insufficient permissions',
	            'details' => []
	        ];
	    }

	    // Use new secure database class
	    $database = new Gotham_Database();
	    return $database->reset_analytics_data() ?
	        ['success' => true, 'message' => 'Analytics data reset successfully', 'details' => []] :
	        ['success' => false, 'message' => 'Failed to reset analytics data', 'details' => []];
	}

	// Function to ensure bot detection columns exist
	function gotham_ensure_bot_detection_columns() {
	    global $wpdb;
	    $table = $wpdb->prefix . 'gotham_adblock_stats';

	    // Check if table exists
	    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
	        return ['success' => false, 'message' => 'Analytics table does not exist'];
	    }

	    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
	    $column_names = array_column($columns, 'Field');

	    $migrations_run = [];
	    $errors = [];

	    // Add missing bot detection columns
	    if (!in_array('is_bot', $column_names)) {
	        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN is_bot tinyint(1) DEFAULT 0 AFTER is_mobile");
	        if ($result !== false) {
	            $migrations_run[] = 'Added is_bot column';
	        } else {
	            $errors[] = 'Failed to add is_bot column: ' . $wpdb->last_error;
	        }
	    }

	    if (!in_array('bot_type', $column_names)) {
	        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN bot_type varchar(50) DEFAULT '' AFTER is_bot");
	        if ($result !== false) {
	            $migrations_run[] = 'Added bot_type column';
	        } else {
	            $errors[] = 'Failed to add bot_type column: ' . $wpdb->last_error;
	        }
	    }

	    if (!in_array('bot_confidence', $column_names)) {
	        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN bot_confidence int(3) DEFAULT 0 AFTER bot_type");
	        if ($result !== false) {
	            $migrations_run[] = 'Added bot_confidence column';
	        } else {
	            $errors[] = 'Failed to add bot_confidence column: ' . $wpdb->last_error;
	        }
	    }

	    // Add indexes for better performance
	    $indexes_to_add = [
	        'is_bot' => "ALTER TABLE $table ADD INDEX idx_is_bot (is_bot)",
	        'human_traffic' => "ALTER TABLE $table ADD INDEX idx_human_traffic (is_bot, event_type, created_at)"
	    ];

	    foreach ($indexes_to_add as $index_name => $sql) {
	        // Check if index exists
	        $index_exists = $wpdb->get_var("SHOW INDEX FROM $table WHERE Key_name = 'idx_$index_name'");
	        if (!$index_exists) {
	            $result = $wpdb->query($sql);
	            if ($result !== false) {
	                $migrations_run[] = "Added index: $index_name";
	            } else {
	                $errors[] = "Failed to add index $index_name: " . $wpdb->last_error;
	            }
	        }
	    }

	    if (!empty($errors)) {
	        return [
	            'success' => false,
	            'message' => 'Some migrations failed: ' . implode(', ', $errors),
	            'migrations_run' => $migrations_run,
	            'errors' => $errors
	        ];
	    }

	    if (empty($migrations_run)) {
	        return ['success' => true, 'message' => 'Bot detection columns already exist'];
	    }

	    return [
	        'success' => true,
	        'message' => 'Bot detection columns added successfully: ' . implode(', ', $migrations_run),
	        'migrations_run' => $migrations_run
	    ];
	}

	// Retroactive Bot Detection Function
	function gotham_apply_retroactive_bot_detection() {
	    global $wpdb;
	    $table = $wpdb->prefix . 'gotham_adblock_stats';

	    // First ensure bot detection columns exist
	    $column_check = gotham_ensure_bot_detection_columns();
	    if (!$column_check['success']) {
	        return $column_check;
	    }

	    // Check if table exists and has bot detection columns
	    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
	        return ['success' => false, 'message' => 'Analytics table does not exist'];
	    }

	    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
	    $column_names = array_column($columns, 'Field');

	    if (!in_array('is_bot', $column_names)) {
	        return ['success' => false, 'message' => 'Bot detection columns do not exist after migration attempt'];
	    }

	    // Get all records that haven't been processed for bot detection
	    $records = $wpdb->get_results(
	        "SELECT id, user_agent, ip_address FROM $table
	         WHERE (is_bot IS NULL OR is_bot = 0) AND user_agent != '' AND user_agent IS NOT NULL
	         ORDER BY id ASC"
	    );

	    $processed = 0;
	    $bots_detected = 0;

	    foreach ($records as $record) {
	        // Temporarily set global variables for bot detection
	        $_SERVER['HTTP_USER_AGENT'] = $record->user_agent;
	        $_SERVER['REMOTE_ADDR'] = $record->ip_address;

	        // Clear any POST data that might interfere with detection
	        $original_post = $_POST;
	        $_POST = [];

	        // Set up minimal HTTP headers for retroactive analysis
	        if (!isset($_SERVER['HTTP_ACCEPT'])) $_SERVER['HTTP_ACCEPT'] = '';
	        if (!isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) $_SERVER['HTTP_ACCEPT_LANGUAGE'] = '';
	        if (!isset($_SERVER['HTTP_ACCEPT_ENCODING'])) $_SERVER['HTTP_ACCEPT_ENCODING'] = '';
	        if (!isset($_SERVER['HTTP_REFERER'])) $_SERVER['HTTP_REFERER'] = '';

	        // Run simplified bot detection for retroactive analysis
	        $bot_detection = gotham_detect_bot_retroactive($record->user_agent, $record->ip_address);

	        // Debug logging for first few records and all bot detections
	        if ($processed < 10 || $bot_detection['is_bot']) {
	            error_log("Gotham Bot Detection Debug - Record {$record->id}: UA=" . substr($record->user_agent, 0, 100) . ", Score={$bot_detection['confidence']}, IsBot=" . ($bot_detection['is_bot'] ? 'YES' : 'NO') . ", Type={$bot_detection['bot_type']}, Reason=" . $bot_detection['reason']);
	        }

	        // Update record with bot detection results
	        $update_result = $wpdb->update(
	            $table,
	            [
	                'is_bot' => $bot_detection['is_bot'] ? 1 : 0,
	                'bot_type' => $bot_detection['bot_type'] ?? '',
	                'bot_confidence' => $bot_detection['confidence'] ?? 0
	            ],
	            ['id' => $record->id]
	        );

	        if ($update_result !== false) {
	            $processed++;
	            if ($bot_detection['is_bot']) {
	                $bots_detected++;
	            }
	        }

	        // Restore POST data
	        $_POST = $original_post;
	    }

	    // Clean up global variables
	    unset($_SERVER['HTTP_USER_AGENT']);
	    unset($_SERVER['REMOTE_ADDR']);

	    return [
	        'success' => true,
	        'message' => "Retroactive bot detection completed. Processed $processed records, detected $bots_detected bots.",
	        'processed' => $processed,
	        'bots_detected' => $bots_detected
	    ];
	}

	// Test function to verify bot detection columns
	function gotham_test_bot_detection_columns() {
	    global $wpdb;
	    $table = $wpdb->prefix . 'gotham_adblock_stats';

	    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
	        return ['success' => false, 'message' => 'Analytics table does not exist'];
	    }

	    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
	    $column_names = array_column($columns, 'Field');

	    $required_bot_columns = ['is_bot', 'bot_type', 'bot_confidence'];
	    $missing_columns = array_diff($required_bot_columns, $column_names);

	    if (empty($missing_columns)) {
	        return [
	            'success' => true,
	            'message' => 'All bot detection columns exist',
	            'columns' => $column_names
	        ];
	    } else {
	        return [
	            'success' => false,
	            'message' => 'Missing bot detection columns: ' . implode(', ', $missing_columns),
	            'missing_columns' => $missing_columns,
	            'existing_columns' => $column_names
	        ];
	    }
	}

	// Simplified Bot Detection for Retroactive Analysis
	function gotham_detect_bot_retroactive($user_agent, $ip_address) {
	    $score = 0;
	    $reasons = [];
	    $bot_type = 'unknown';

	    // 1. KNOWN BOT USER AGENTS (High confidence detection)
	    $known_bots = [
	        // Search Engine Bots (Legitimate - score 0)
	        'googlebot' => ['type' => 'search_engine', 'score' => 0],
	        'bingbot' => ['type' => 'search_engine', 'score' => 0],
	        'slurp' => ['type' => 'search_engine', 'score' => 0],
	        'duckduckbot' => ['type' => 'search_engine', 'score' => 0],
	        'baiduspider' => ['type' => 'search_engine', 'score' => 0],
	        'yandexbot' => ['type' => 'search_engine', 'score' => 0],

	        // Social Media Crawlers (Legitimate)
	        'facebookexternalhit' => ['type' => 'social_crawler', 'score' => 0],
	        'twitterbot' => ['type' => 'social_crawler', 'score' => 0],
	        'linkedinbot' => ['type' => 'social_crawler', 'score' => 0],
	        'whatsapp' => ['type' => 'social_crawler', 'score' => 0],

	        // Malicious/Suspicious Bots
	        'phantomjs' => ['type' => 'headless_browser', 'score' => 95],
	        'headlesschrome' => ['type' => 'headless_browser', 'score' => 95],
	        'selenium' => ['type' => 'automation', 'score' => 90],
	        'webdriver' => ['type' => 'automation', 'score' => 90],
	        'puppeteer' => ['type' => 'automation', 'score' => 90],
	        'playwright' => ['type' => 'automation', 'score' => 90],
	        'scrapy' => ['type' => 'scraper', 'score' => 85],
	        'python-requests' => ['type' => 'scraper', 'score' => 85],
	        'curl' => ['type' => 'tool', 'score' => 80],
	        'wget' => ['type' => 'tool', 'score' => 80],
	        'httpie' => ['type' => 'tool', 'score' => 75],
	        'postman' => ['type' => 'tool', 'score' => 70],
	        'ahrefsbot' => ['type' => 'seo_bot', 'score' => 75],
	        'semrushbot' => ['type' => 'seo_bot', 'score' => 75],
	        'mj12bot' => ['type' => 'seo_bot', 'score' => 75],
	        'dotbot' => ['type' => 'seo_bot', 'score' => 75],
	    ];

	    $user_agent_lower = strtolower($user_agent);
	    foreach ($known_bots as $bot_pattern => $bot_info) {
	        if (strpos($user_agent_lower, $bot_pattern) !== false) {
	            $score = $bot_info['score'];
	            $bot_type = $bot_info['type'];
	            $reasons[] = "Known bot pattern: $bot_pattern";
	            break;
	        }
	    }

	    // 2. BASIC USER AGENT VALIDATION (only if not already detected)
	    if ($score == 0) {
	        if (empty($user_agent)) {
	            $score = 85;
	            $reasons[] = 'Empty user agent';
	            $bot_type = 'missing_ua';
	        } elseif (strlen($user_agent) < 10) {
	            $score = 80;
	            $reasons[] = 'User agent too short';
	            $bot_type = 'suspicious_ua';
	        } elseif (strlen($user_agent) > 1000) {
	            $score = 70;
	            $reasons[] = 'User agent suspiciously long';
	            $bot_type = 'suspicious_ua';
	        }
	    }

	    // 3. PROGRAMMING LANGUAGE PATTERNS
	    if ($score < 70) { // Only check if not already flagged
	        $programming_patterns = [
	            'python' => 80, 'java/' => 75, 'php/' => 75, 'ruby' => 75, 'perl' => 75,
	            'go-http' => 85, 'node.js' => 70, 'axios' => 65, 'urllib' => 80,
	            'libwww' => 75, 'lwp' => 75, 'mechanize' => 80, 'requests' => 85
	        ];

	        foreach ($programming_patterns as $pattern => $pattern_score) {
	            if (stripos($user_agent, $pattern) !== false) {
	                $score = $pattern_score;
	                $reasons[] = "Programming language pattern: $pattern";
	                $bot_type = 'programming_ua';
	                break;
	            }
	        }
	    }

	    // 4. GENERIC BOT PATTERNS
	    if ($score < 70) { // Only check if not already flagged
	        $generic_bot_patterns = [
	            'bot' => 70, 'crawler' => 70, 'spider' => 70, 'scraper' => 75,
	            'fetcher' => 70, 'checker' => 65, 'monitor' => 65, 'validator' => 65,
	            'analyzer' => 65, 'scanner' => 75, 'harvester' => 80
	        ];

	        foreach ($generic_bot_patterns as $pattern => $pattern_score) {
	            if (stripos($user_agent, $pattern) !== false) {
	                $score = $pattern_score;
	                $reasons[] = "Generic bot pattern: $pattern";
	                $bot_type = 'generic_bot';
	                break;
	            }
	        }
	    }

	    $final_score = min(100, $score);
	    $is_bot = $final_score >= 70;

	    return [
	        'is_bot' => $is_bot,
	        'confidence' => $final_score,
	        'bot_type' => $bot_type,
	        'reason' => implode('; ', $reasons),
	        'user_agent' => $user_agent,
	        'detection_method' => 'retroactive_analysis'
	    ];
	}



	// Automatic Bot Detection Function - DEPRECATED: Use new class-based system
	function gotham_auto_apply_bot_detection() {
	    // Use new secure bot detector class
	    if (class_exists('Gotham_Bot_Detector')) {
	        $bot_detector = new Gotham_Bot_Detector();
	        $result = $bot_detector->batch_detect_bots(500, 0);

	        if ($result['processed'] > 0) {
	            error_log("Gotham Analytics: Automatic bot detection processed {$result['processed']} records, updated {$result['updated']} records");
	        }
	    }
	}

} // Fin de l'Admin

// Create analytics table on plugin activation
register_activation_hook(__FILE__, 'gotham_create_analytics_table');
function gotham_create_analytics_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';
    $charset_collate = $wpdb->get_charset_collate();

    // Use direct SQL instead of dbDelta for better compatibility
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        browser varchar(100),
        browser_version varchar(50) DEFAULT '',
        os varchar(50),
        country varchar(100),
        status varchar(20) DEFAULT 'pending',
        user_type varchar(20) DEFAULT 'unknown',
        session_id varchar(100) DEFAULT '',
        referrer_url text,
        page_url text,
        session_duration int(11) DEFAULT 0,
        is_mobile tinyint(1) DEFAULT 0,
        is_bot tinyint(1) DEFAULT 0,
        bot_type varchar(50) DEFAULT '',
        bot_confidence int(3) DEFAULT 0,
        screen_resolution varchar(20) DEFAULT '',
        timezone varchar(50) DEFAULT '',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY ip_address (ip_address),
        KEY status (status),
        KEY event_type (event_type),
        KEY created_at (created_at),
        KEY user_type (user_type),
        KEY session_id (session_id),
        KEY is_bot (is_bot),
        KEY human_traffic (is_bot, event_type, created_at)
    ) $charset_collate;";

    $result = $wpdb->query($sql);

    if ($result === false) {
        error_log('Gotham Analytics: Table creation failed - ' . $wpdb->last_error);
        return false;
    }

    error_log('Gotham Analytics: Table created successfully');
    return true;
}

// Enhanced Bot Detection Function with Weighted Scoring System
function gotham_detect_bot() {
    $start_time = microtime(true);

    // Gather request data
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $accept = $_SERVER['HTTP_ACCEPT'] ?? '';
    $accept_language = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
    $accept_encoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '';
    $request_method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';

    // Initialize scoring system
    $scores = [];
    $reasons = [];
    $bot_types = [];

    // Detection threshold (70+ = bot)
    $bot_threshold = 70;

    // 1. ENHANCED USER-AGENT ANALYSIS (Weight: 40%)
    $ua_analysis = gotham_analyze_user_agent($user_agent);
    $scores['user_agent'] = $ua_analysis['score'];
    if ($ua_analysis['score'] > 0) {
        $reasons[] = $ua_analysis['reason'];
        $bot_types[] = $ua_analysis['type'];
    }

    // 2. HTTP HEADER ANALYSIS (Weight: 25%)
    $header_analysis = gotham_analyze_http_headers($accept, $accept_language, $accept_encoding, $referer);
    $scores['headers'] = $header_analysis['score'];
    if ($header_analysis['score'] > 0) {
        $reasons[] = $header_analysis['reason'];
        $bot_types[] = $header_analysis['type'];
    }

    // 3. IP REPUTATION & RATE LIMITING (Weight: 20%)
    $ip_analysis = gotham_analyze_ip_reputation($ip);
    $scores['ip_reputation'] = $ip_analysis['score'];
    if ($ip_analysis['score'] > 0) {
        $reasons[] = $ip_analysis['reason'];
        $bot_types[] = $ip_analysis['type'];
    }

    // 4. BEHAVIORAL ANALYSIS (Weight: 10%)
    $behavior_analysis = gotham_analyze_behavior_patterns();
    $scores['behavior'] = $behavior_analysis['score'];
    if ($behavior_analysis['score'] > 0) {
        $reasons[] = $behavior_analysis['reason'];
        $bot_types[] = $behavior_analysis['type'];
    }

    // 5. JAVASCRIPT FINGERPRINTING (Weight: 5%)
    $js_analysis = gotham_analyze_javascript_fingerprint();
    $scores['javascript'] = $js_analysis['score'];
    if ($js_analysis['score'] > 0) {
        $reasons[] = $js_analysis['reason'];
        $bot_types[] = $js_analysis['type'];
    }

    // Calculate weighted final score
    $weights = [
        'user_agent' => 0.40,
        'headers' => 0.25,
        'ip_reputation' => 0.20,
        'behavior' => 0.10,
        'javascript' => 0.05
    ];

    $final_score = 0;
    foreach ($scores as $category => $score) {
        $final_score += $score * $weights[$category];
    }

    // Determine primary bot type
    $primary_bot_type = !empty($bot_types) ? $bot_types[0] : 'unknown';

    // Performance check
    $processing_time = (microtime(true) - $start_time) * 1000; // Convert to milliseconds

    return [
        'is_bot' => $final_score >= $bot_threshold,
        'confidence' => min(100, round($final_score)),
        'bot_type' => $primary_bot_type,
        'reason' => implode('; ', array_filter($reasons)),
        'user_agent' => $user_agent,
        'scores' => $scores,
        'processing_time_ms' => round($processing_time, 2),
        'detection_details' => [
            'user_agent_analysis' => $ua_analysis,
            'header_analysis' => $header_analysis,
            'ip_analysis' => $ip_analysis,
            'behavior_analysis' => $behavior_analysis,
            'js_analysis' => $js_analysis
        ]
    ];
}

// Enhanced User-Agent Analysis with Entropy and Pattern Detection
function gotham_analyze_user_agent($user_agent) {
    $score = 0;
    $reasons = [];
    $type = 'unknown';

    // 1. LEGITIMATE CRAWLER WHITELIST (with verification)
    $legitimate_crawlers = [
        'googlebot' => ['google.com', 'googlebot.com'],
        'bingbot' => ['search.msn.com'],
        'slurp' => ['crawl.yahoo.net'],
        'duckduckbot' => ['duckduckgo.com'],
        'baiduspider' => ['baidu.com', 'baidu.jp'],
        'yandexbot' => ['yandex.com', 'yandex.net', 'yandex.ru'],
        'facebookexternalhit' => ['facebook.com'],
        'twitterbot' => ['twitter.com'],
        'linkedinbot' => ['linkedin.com']
    ];

    $user_agent_lower = strtolower($user_agent);

    // Check for legitimate crawlers
    foreach ($legitimate_crawlers as $crawler => $valid_domains) {
        if (strpos($user_agent_lower, $crawler) !== false) {
            // TODO: Implement reverse DNS verification for production
            // For now, treat as legitimate (score 0)
            return ['score' => 0, 'reason' => "Legitimate crawler: $crawler", 'type' => 'legitimate_crawler'];
        }
    }

    // 2. KNOWN MALICIOUS BOTS
    $malicious_bots = [
        // Headless browsers and automation
        'phantomjs', 'headlesschrome', 'selenium', 'webdriver', 'puppeteer', 'playwright',
        'chrome-lighthouse', 'jsdom', 'htmlunit', 'zombie.js',

        // Scrapers and crawlers
        'scrapy', 'python-requests', 'curl', 'wget', 'httpie', 'postman', 'insomnia',
        'rest-client', 'apache-httpclient', 'okhttp', 'urllib', 'mechanize',

        // Security scanners
        'nmap', 'masscan', 'zmap', 'nuclei', 'sqlmap', 'nikto', 'burp', 'owasp',
        'w3af', 'skipfish', 'dirb', 'gobuster', 'wfuzz',

        // SEO tools (aggressive)
        'ahrefsbot', 'semrushbot', 'mj12bot', 'dotbot', 'rogerbot', 'exabot',
        'screaming frog', 'sitebulb', 'deepcrawl', 'botify',

        // Generic patterns
        'bot', 'crawler', 'spider', 'scraper', 'fetcher', 'checker',
        'monitor', 'validator', 'analyzer', 'scanner', 'harvester'
    ];

    foreach ($malicious_bots as $bot_pattern) {
        if (strpos($user_agent_lower, $bot_pattern) !== false) {
            $score = 95;
            $reasons[] = "Known bot pattern: $bot_pattern";
            $type = 'known_malicious_bot';
            break;
        }
    }

    if ($score > 0) {
        return ['score' => $score, 'reason' => implode('; ', $reasons), 'type' => $type];
    }

    // 3. BASIC VALIDATION
    if (empty($user_agent)) {
        return ['score' => 85, 'reason' => 'Empty user agent', 'type' => 'missing_ua'];
    }

    if (strlen($user_agent) < 10) {
        return ['score' => 80, 'reason' => 'User agent too short', 'type' => 'suspicious_ua'];
    }

    if (strlen($user_agent) > 1000) {
        return ['score' => 70, 'reason' => 'User agent suspiciously long', 'type' => 'suspicious_ua'];
    }

    // 4. ENTROPY ANALYSIS
    $entropy = gotham_calculate_entropy($user_agent);
    if ($entropy < 2.5) {
        $score += 30;
        $reasons[] = "Low entropy user agent (repetitive): $entropy";
        $type = 'low_entropy_ua';
    }

    // 5. PROGRAMMING LANGUAGE PATTERNS
    $programming_patterns = [
        'python' => 40, 'java/' => 35, 'php/' => 35, 'ruby' => 35, 'perl' => 35,
        'go-http' => 45, 'node.js' => 40, 'axios' => 30, 'requests' => 45,
        'urllib' => 40, 'libwww' => 35, 'lwp' => 35
    ];

    foreach ($programming_patterns as $pattern => $pattern_score) {
        if (stripos($user_agent, $pattern) !== false) {
            $score += $pattern_score;
            $reasons[] = "Programming language pattern: $pattern";
            $type = 'programming_ua';
            break;
        }
    }

    // 6. SUSPICIOUS CHARACTERISTICS
    $suspicious_patterns = [
        '/^[a-zA-Z0-9\s\-\.\/]+$/' => ['score' => 25, 'reason' => 'Overly simple character set'],
        '/\d{10,}/' => ['score' => 20, 'reason' => 'Contains long numeric sequences'],
        '/^.{1,20}$/' => ['score' => 35, 'reason' => 'Suspiciously short'],
        '/^Mozilla\/[1-4]\./' => ['score' => 30, 'reason' => 'Outdated Mozilla version'],
    ];

    foreach ($suspicious_patterns as $pattern => $details) {
        if (preg_match($pattern, $user_agent)) {
            $score += $details['score'];
            $reasons[] = $details['reason'];
            if ($type === 'unknown') $type = 'suspicious_pattern';
        }
    }

    // 7. MISSING STANDARD COMPONENTS
    $browser_keywords = ['mozilla', 'webkit', 'gecko', 'chrome', 'safari', 'firefox', 'edge', 'opera'];
    $has_browser_keyword = false;
    foreach ($browser_keywords as $keyword) {
        if (stripos($user_agent, $keyword) !== false) {
            $has_browser_keyword = true;
            break;
        }
    }

    if (!$has_browser_keyword) {
        $score += 40;
        $reasons[] = 'Missing standard browser keywords';
        if ($type === 'unknown') $type = 'non_browser_ua';
    }

    // 8. "OTHER" BROWSER DETECTION
    $browser_type = gotham_get_browser_type($user_agent);
    if ($browser_type === 'Other' || $browser_type === 'Unknown') {
        $score += 35;
        $reasons[] = 'Classified as Other/Unknown browser';
        if ($type === 'unknown') $type = 'other_browser';
    }

    return [
        'score' => min(100, $score),
        'reason' => implode('; ', $reasons),
        'type' => $type,
        'entropy' => $entropy ?? null,
        'browser_type' => $browser_type ?? null
    ];
}

// HTTP Header Analysis for Bot Detection
function gotham_analyze_http_headers($accept, $accept_language, $accept_encoding, $referer) {
    $score = 0;
    $reasons = [];
    $type = 'unknown';

    // 1. MISSING CRITICAL HEADERS
    if (empty($accept)) {
        $score += 40;
        $reasons[] = 'Missing Accept header';
        $type = 'missing_headers';
    }

    if (empty($accept_language)) {
        $score += 35;
        $reasons[] = 'Missing Accept-Language header';
        if ($type === 'unknown') $type = 'missing_headers';
    }

    if (empty($accept_encoding)) {
        $score += 30;
        $reasons[] = 'Missing Accept-Encoding header';
        if ($type === 'unknown') $type = 'missing_headers';
    }

    // 2. SUSPICIOUS ACCEPT HEADER PATTERNS
    if (!empty($accept)) {
        $suspicious_accept_patterns = [
            '*/*' => 25,
            'application/json' => 35,
            'text/plain' => 30,
            'application/xml' => 30,
            'text/html; charset=utf-8' => 20,
            'application/x-www-form-urlencoded' => 25
        ];

        foreach ($suspicious_accept_patterns as $pattern => $pattern_score) {
            if (trim($accept) === $pattern) {
                $score += $pattern_score;
                $reasons[] = "Suspicious Accept header: $pattern";
                if ($type === 'unknown') $type = 'suspicious_headers';
                break;
            }
        }

        // Check for browser-like content types
        $has_browser_types = preg_match('/text\/html|application\/xhtml|text\/css|image\/|application\/javascript/', $accept);
        $is_very_short = strlen(trim($accept)) < 20;

        if (!$has_browser_types && $is_very_short) {
            $score += 25;
            $reasons[] = 'Accept header lacks browser-like content types';
            if ($type === 'unknown') $type = 'non_browser_headers';
        }
    }

    // 3. ACCEPT-LANGUAGE ANALYSIS
    if (!empty($accept_language)) {
        // Check for overly simple language headers
        if (preg_match('/^[a-z]{2}$/', $accept_language)) {
            $score += 15;
            $reasons[] = 'Overly simple Accept-Language header';
            if ($type === 'unknown') $type = 'simple_headers';
        }

        // Check for suspicious language patterns
        $suspicious_languages = ['*', 'en', 'en-us', 'en-US'];
        if (in_array(trim($accept_language), $suspicious_languages)) {
            $score += 10;
            $reasons[] = 'Generic Accept-Language header';
            if ($type === 'unknown') $type = 'generic_headers';
        }
    }

    // 4. ACCEPT-ENCODING ANALYSIS
    if (!empty($accept_encoding)) {
        // Most browsers support gzip, deflate, br
        $has_gzip = strpos($accept_encoding, 'gzip') !== false;
        $has_deflate = strpos($accept_encoding, 'deflate') !== false;

        if (!$has_gzip && !$has_deflate) {
            $score += 20;
            $reasons[] = 'Missing standard compression support';
            if ($type === 'unknown') $type = 'unusual_encoding';
        }
    }

    return [
        'score' => min(100, $score),
        'reason' => implode('; ', $reasons),
        'type' => $type
    ];
}

// IP Reputation and Rate Limiting Analysis
function gotham_analyze_ip_reputation($ip) {
    $score = 0;
    $reasons = [];
    $type = 'unknown';

    // 1. RATE LIMITING CHECK
    $rate_limit_check = gotham_check_rate_limit($ip);
    if ($rate_limit_check['is_suspicious']) {
        $score += 60;
        $reasons[] = "High request rate: {$rate_limit_check['requests_per_minute']} req/min";
        $type = 'rate_limit_exceeded';
    }

    // 2. IP RANGE ANALYSIS
    $ip_analysis = gotham_analyze_ip_range($ip);
    if ($ip_analysis['is_suspicious']) {
        $score += $ip_analysis['score'];
        $reasons[] = $ip_analysis['reason'];
        if ($type === 'unknown') $type = $ip_analysis['type'];
    }

    // 3. GEOLOCATION ANALYSIS
    $geo_analysis = gotham_analyze_geolocation($ip);
    if ($geo_analysis['is_suspicious']) {
        $score += $geo_analysis['score'];
        $reasons[] = $geo_analysis['reason'];
        if ($type === 'unknown') $type = $geo_analysis['type'];
    }

    // 4. HISTORICAL BEHAVIOR
    $history_analysis = gotham_analyze_ip_history($ip);
    if ($history_analysis['is_suspicious']) {
        $score += $history_analysis['score'];
        $reasons[] = $history_analysis['reason'];
        if ($type === 'unknown') $type = $history_analysis['type'];
    }

    return [
        'score' => min(100, $score),
        'reason' => implode('; ', $reasons),
        'type' => $type,
        'rate_limit_data' => $rate_limit_check,
        'ip_range_data' => $ip_analysis,
        'geo_data' => $geo_analysis,
        'history_data' => $history_analysis
    ];
}

// Behavioral Analysis for Bot Detection
function gotham_analyze_behavior_patterns() {
    $score = 0;
    $reasons = [];
    $type = 'unknown';

    // Get behavioral data from client-side JavaScript
    $behavior_score = $_POST['behavior_score'] ?? 0;
    $mouse_movements = $_POST['mouse_movements'] ?? 0;
    $click_timing = $_POST['click_timing'] ?? 0;
    $scroll_behavior = $_POST['scroll_behavior'] ?? 0;
    $interaction_depth = $_POST['interaction_depth'] ?? 0;
    $session_duration = $_POST['session_duration'] ?? 0;

    // 1. LACK OF HUMAN BEHAVIOR
    if ($behavior_score == 0 && $mouse_movements == 0 && $click_timing == 0) {
        $score += 30;
        $reasons[] = 'No behavioral data detected';
        $type = 'no_behavior';
    }

    // 2. SUSPICIOUS TIMING PATTERNS
    if ($click_timing > 0 && $click_timing < 50) { // Clicks faster than 50ms
        $score += 25;
        $reasons[] = 'Inhuman click timing';
        if ($type === 'unknown') $type = 'automated_timing';
    }

    // 3. LACK OF MOUSE MOVEMENT
    if ($mouse_movements == 0 && $interaction_depth > 0) {
        $score += 20;
        $reasons[] = 'No mouse movement with interactions';
        if ($type === 'unknown') $type = 'no_mouse_movement';
    }

    // 4. ABNORMAL SESSION PATTERNS
    if ($session_duration > 0 && $session_duration < 1000) { // Less than 1 second
        $score += 15;
        $reasons[] = 'Extremely short session duration';
        if ($type === 'unknown') $type = 'short_session';
    }

    // 5. POSITIVE HUMAN INDICATORS (reduce score)
    if ($behavior_score > 5 && $mouse_movements > 10 && $click_timing > 100) {
        $score = max(0, $score - 40);
        if (empty($reasons)) {
            $reasons[] = 'Strong human behavioral indicators';
            $type = 'likely_human';
        }
    }

    return [
        'score' => min(100, $score),
        'reason' => implode('; ', $reasons),
        'type' => $type,
        'behavior_data' => [
            'behavior_score' => $behavior_score,
            'mouse_movements' => $mouse_movements,
            'click_timing' => $click_timing,
            'scroll_behavior' => $scroll_behavior,
            'interaction_depth' => $interaction_depth,
            'session_duration' => $session_duration
        ]
    ];
}

// JavaScript Fingerprinting Analysis
function gotham_analyze_javascript_fingerprint() {
    $score = 0;
    $reasons = [];
    $type = 'unknown';

    // Get JavaScript fingerprint data
    $webdriver_detected = $_POST['webdriver_detected'] ?? 'false';
    $canvas_fingerprint = $_POST['canvas_fingerprint'] ?? '';
    $webgl_vendor = $_POST['webgl_vendor'] ?? '';
    $webgl_renderer = $_POST['webgl_renderer'] ?? '';
    $screen_resolution = $_POST['screen_resolution'] ?? '';
    $timezone = $_POST['timezone'] ?? '';
    $plugins_count = $_POST['plugins_count'] ?? 0;

    // 1. WEBDRIVER DETECTION
    if ($webdriver_detected === 'true') {
        $score += 80;
        $reasons[] = 'WebDriver automation detected';
        $type = 'webdriver_detected';
    }

    // 2. HEADLESS BROWSER INDICATORS
    if (empty($canvas_fingerprint) || $canvas_fingerprint === 'blocked') {
        $score += 25;
        $reasons[] = 'Canvas fingerprinting blocked or unavailable';
        if ($type === 'unknown') $type = 'headless_browser';
    }

    // 3. SUSPICIOUS WEBGL RENDERER
    $suspicious_renderers = ['swiftshader', 'llvmpipe', 'software', 'mesa'];
    foreach ($suspicious_renderers as $renderer) {
        if (stripos($webgl_renderer, $renderer) !== false) {
            $score += 20;
            $reasons[] = "Suspicious WebGL renderer: $renderer";
            if ($type === 'unknown') $type = 'suspicious_renderer';
            break;
        }
    }

    // 4. MISSING PLUGINS
    if ($plugins_count == 0) {
        $score += 15;
        $reasons[] = 'No browser plugins detected';
        if ($type === 'unknown') $type = 'no_plugins';
    }

    // 5. INCONSISTENT SCREEN RESOLUTION
    if (!empty($screen_resolution) && preg_match('/(\d+)x(\d+)/', $screen_resolution, $matches)) {
        $width = intval($matches[1]);
        $height = intval($matches[2]);

        // Very common bot resolutions
        $bot_resolutions = ['1024x768', '800x600', '1920x1080'];
        if (in_array($screen_resolution, $bot_resolutions) && $width * $height < 1000000) {
            $score += 10;
            $reasons[] = 'Common bot screen resolution';
            if ($type === 'unknown') $type = 'bot_resolution';
        }
    }

    return [
        'score' => min(100, $score),
        'reason' => implode('; ', $reasons),
        'type' => $type,
        'fingerprint_data' => [
            'webdriver_detected' => $webdriver_detected,
            'canvas_fingerprint' => substr($canvas_fingerprint, 0, 50),
            'webgl_vendor' => $webgl_vendor,
            'webgl_renderer' => $webgl_renderer,
            'screen_resolution' => $screen_resolution,
            'timezone' => $timezone,
            'plugins_count' => $plugins_count
        ]
    ];
}



// Enhanced Rate Limiting Check for Bot Detection
function gotham_check_rate_limit($ip) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check requests in last minute
    $one_minute_ago = date('Y-m-d H:i:s', strtotime('-1 minute'));
    $requests_last_minute = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND created_at > %s",
        $ip, $one_minute_ago
    ));

    // Check requests in last hour
    $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
    $requests_last_hour = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND created_at > %s",
        $ip, $one_hour_ago
    ));

    // Check requests in last 24 hours
    $one_day_ago = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $requests_last_day = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND created_at > %s",
        $ip, $one_day_ago
    ));

    // Determine suspicion level
    $is_suspicious = false;
    $suspicion_level = 'normal';

    if ($requests_last_minute > 15) {
        $is_suspicious = true;
        $suspicion_level = 'very_high';
    } elseif ($requests_last_minute > 10) {
        $is_suspicious = true;
        $suspicion_level = 'high';
    } elseif ($requests_last_hour > 100) {
        $is_suspicious = true;
        $suspicion_level = 'high';
    } elseif ($requests_last_hour > 50) {
        $is_suspicious = true;
        $suspicion_level = 'medium';
    } elseif ($requests_last_day > 500) {
        $is_suspicious = true;
        $suspicion_level = 'medium';
    }

    return [
        'is_suspicious' => $is_suspicious,
        'suspicion_level' => $suspicion_level,
        'requests_per_minute' => $requests_last_minute,
        'requests_per_hour' => $requests_last_hour,
        'requests_per_day' => $requests_last_day
    ];
}

// Calculate entropy of a string (for user-agent analysis)
function gotham_calculate_entropy($string) {
    if (empty($string)) return 0;

    $length = strlen($string);
    $frequency = array_count_values(str_split($string));
    $entropy = 0;

    foreach ($frequency as $count) {
        $probability = $count / $length;
        $entropy -= $probability * log($probability, 2);
    }

    return round($entropy, 2);
}

// Analyze IP range for suspicious patterns
function gotham_analyze_ip_range($ip) {
    $score = 0;
    $reasons = [];
    $type = 'unknown';
    $is_suspicious = false;

    // 1. CHECK FOR PRIVATE/LOCAL IPs
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        // This is a private or reserved IP
        return [
            'is_suspicious' => false,
            'score' => 0,
            'reason' => 'Private/local IP address',
            'type' => 'private_ip'
        ];
    }

    // 2. KNOWN HOSTING PROVIDER RANGES (simplified check)
    $hosting_patterns = [
        // AWS
        '/^(3\.|13\.|15\.|18\.|34\.|35\.|52\.|54\.|107\.|174\.|184\.|204\.|207\.)/' => 'AWS',
        // Google Cloud
        '/^(8\.8\.|8\.34\.|23\.|35\.|104\.|107\.|130\.|146\.|162\.|199\.)/' => 'Google Cloud',
        // DigitalOcean
        '/^(104\.131\.|104\.236\.|107\.170\.|138\.197\.|159\.203\.|162\.243\.|164\.90\.|165\.227\.|167\.71\.|167\.99\.|178\.62\.|188\.166\.|188\.226\.|192\.241\.|206\.189\.|206\.81\.|209\.97\.)/' => 'DigitalOcean',
        // Linode
        '/^(45\.33\.|45\.56\.|45\.79\.|50\.116\.|66\.175\.|66\.228\.|69\.164\.|72\.14\.|74\.207\.|96\.126\.|97\.107\.|139\.162\.|172\.104\.|173\.230\.|173\.255\.|176\.58\.|178\.79\.|192\.46\.|192\.53\.|198\.58\.|212\.71\.|213\.219\.)/' => 'Linode'
    ];

    foreach ($hosting_patterns as $pattern => $provider) {
        if (preg_match($pattern, $ip)) {
            $score = 25;
            $reasons[] = "Hosting provider IP: $provider";
            $type = 'hosting_provider';
            $is_suspicious = true;
            break;
        }
    }

    // 3. TOR EXIT NODES (simplified check)
    $tor_patterns = [
        '/^(199\.87\.|192\.42\.|171\.25\.|185\.220\.|109\.70\.|77\.247\.)/',
    ];

    foreach ($tor_patterns as $pattern) {
        if (preg_match($pattern, $ip)) {
            $score = 40;
            $reasons[] = 'Potential Tor exit node';
            $type = 'tor_exit_node';
            $is_suspicious = true;
            break;
        }
    }

    return [
        'is_suspicious' => $is_suspicious,
        'score' => $score,
        'reason' => implode('; ', $reasons),
        'type' => $type
    ];
}

// Analyze geolocation patterns
function gotham_analyze_geolocation($ip) {
    // Simplified geolocation analysis
    // In production, you would integrate with a geolocation service

    $score = 0;
    $reasons = [];
    $type = 'unknown';
    $is_suspicious = false;

    // Basic country-based analysis (simplified)
    // This would typically use a real geolocation service
    $suspicious_countries = ['CN', 'RU', 'KP', 'IR']; // Example suspicious countries

    // For now, return neutral analysis
    return [
        'is_suspicious' => false,
        'score' => 0,
        'reason' => 'Geolocation analysis not implemented',
        'type' => 'geo_neutral'
    ];
}

// Analyze IP historical behavior
function gotham_analyze_ip_history($ip) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $bot_table = $wpdb->prefix . 'gotham_bot_stats';

    $score = 0;
    $reasons = [];
    $type = 'unknown';
    $is_suspicious = false;

    // 1. CHECK PREVIOUS BOT DETECTIONS
    $previous_bot_detections = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND is_bot = 1",
        $ip
    ));

    if ($previous_bot_detections > 0) {
        $score += 30;
        $reasons[] = "Previously flagged as bot $previous_bot_detections times";
        $type = 'repeat_offender';
        $is_suspicious = true;
    }

    // 2. CHECK BOT STATS TABLE
    if ($wpdb->get_var("SHOW TABLES LIKE '$bot_table'") == $bot_table) {
        $bot_history = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $bot_table WHERE ip_address = %s",
            $ip
        ));

        if ($bot_history > 0) {
            $score += 25;
            $reasons[] = "Found in bot tracking history";
            if ($type === 'unknown') $type = 'bot_history';
            $is_suspicious = true;
        }
    }

    // 3. CHECK UNUSUAL PATTERNS
    $total_requests = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s",
        $ip
    ));

    $unique_user_agents = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT user_agent) FROM $table WHERE ip_address = %s",
        $ip
    ));

    // If many requests but very few user agents, suspicious
    if ($total_requests > 10 && $unique_user_agents <= 2) {
        $score += 20;
        $reasons[] = "Multiple requests with few user agents";
        if ($type === 'unknown') $type = 'pattern_anomaly';
        $is_suspicious = true;
    }

    return [
        'is_suspicious' => $is_suspicious,
        'score' => $score,
        'reason' => implode('; ', $reasons),
        'type' => $type,
        'history_data' => [
            'previous_bot_detections' => $previous_bot_detections,
            'total_requests' => $total_requests,
            'unique_user_agents' => $unique_user_agents
        ]
    ];
}

// Track Bot Traffic Separately
function gotham_track_bot_traffic($bot_detection) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_bot_stats';

    // Create bot stats table if it doesn't exist
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
        gotham_create_bot_stats_table();
    }

    $wpdb->insert($table, [
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'bot_type' => $bot_detection['bot_type'],
        'detection_reason' => $bot_detection['reason'],
        'confidence' => $bot_detection['confidence'],
        'referer' => $_SERVER['HTTP_REFERER'] ?? '',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'created_at' => current_time('mysql')
    ]);
}

// Create Bot Stats Table
function gotham_create_bot_stats_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_bot_stats';
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        bot_type varchar(50),
        detection_reason text,
        confidence int(3),
        referer text,
        request_uri text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY ip_address (ip_address),
        KEY bot_type (bot_type),
        KEY created_at (created_at)
    ) $charset_collate;";

    $wpdb->query($sql);
}

// Debug function to analyze Accept headers from legitimate users
function gotham_log_accept_headers() {
    if (!current_user_can('manage_options')) return;

    $headers = [
        'Accept' => $_SERVER['HTTP_ACCEPT'] ?? 'Not set',
        'Accept-Language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'Not set',
        'Accept-Encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'Not set',
        'User-Agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Not set'
    ];

    error_log('Gotham Debug - Legitimate Browser Headers: ' . print_r($headers, true));
}

// AJAX endpoint for header debugging
add_action('wp_ajax_gotham_debug_headers', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    $headers = [
        'Accept' => $_SERVER['HTTP_ACCEPT'] ?? 'Not set',
        'Accept-Language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'Not set',
        'Accept-Encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'Not set',
        'User-Agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Not set'
    ];

    wp_send_json_success([
        'message' => 'Headers captured successfully',
        'headers' => $headers,
        'bot_detection' => gotham_detect_bot()
    ]);
});

// AJAX endpoint for comprehensive analytics debugging
add_action('wp_ajax_gotham_debug_analytics', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    $bot_table = $wpdb->prefix . 'gotham_bot_stats';

    $debug_info = [];

    // 1. Check if tables exist
    $debug_info['table_exists'] = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    $debug_info['bot_table_exists'] = $wpdb->get_var("SHOW TABLES LIKE '$bot_table'") == $bot_table;

    if ($debug_info['table_exists']) {
        // 2. Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table");
        $debug_info['table_columns'] = array_column($columns, 'Field');

        // 3. Count total records
        $debug_info['total_records'] = $wpdb->get_var("SELECT COUNT(*) FROM $table");

        // 4. Count human vs bot records
        $debug_info['human_records'] = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 0");
        $debug_info['bot_records'] = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 1");

        // 5. Count by event type (human only)
        $debug_info['event_counts'] = $wpdb->get_results(
            "SELECT event_type, COUNT(*) as count, COUNT(DISTINCT ip_address) as unique_ips
             FROM $table WHERE is_bot = 0 GROUP BY event_type"
        );

        // 6. Recent records (last 10)
        $debug_info['recent_records'] = $wpdb->get_results(
            "SELECT id, event_type, ip_address, is_bot, bot_type, created_at
             FROM $table ORDER BY created_at DESC LIMIT 10"
        );

        // 7. Check for missing is_bot column
        $has_is_bot = in_array('is_bot', $debug_info['table_columns']);
        $debug_info['has_bot_detection_columns'] = $has_is_bot;

        if (!$has_is_bot) {
            $debug_info['migration_needed'] = true;
            $debug_info['migration_sql'] = [
                "ALTER TABLE $table ADD COLUMN is_bot tinyint(1) DEFAULT 0",
                "ALTER TABLE $table ADD COLUMN bot_type varchar(50) DEFAULT ''",
                "ALTER TABLE $table ADD COLUMN bot_confidence int(3) DEFAULT 0"
            ];
        }
    }

    // 8. Test AJAX endpoints
    $debug_info['ajax_endpoints'] = [
        'track_event' => has_action('wp_ajax_gotham_adblock_track_event') || has_action('wp_ajax_nopriv_gotham_adblock_track_event'),
        'get_stats' => has_action('wp_ajax_gotham_get_analytics_stats'),
        'track_legacy' => has_action('wp_ajax_gotham_track_adblock_action') || has_action('wp_ajax_nopriv_gotham_track_adblock_action')
    ];

    // 9. Check WordPress settings
    $debug_info['wp_settings'] = [
        'wp_debug' => defined('WP_DEBUG') && WP_DEBUG,
        'wp_debug_log' => defined('WP_DEBUG_LOG') && WP_DEBUG_LOG,
        'ajax_url' => admin_url('admin-ajax.php'),
        'current_user_can_manage' => current_user_can('manage_options')
    ];

    // Additional detailed analysis
    if ($debug_info['table_exists']) {
        // Check for data anomalies
        $debug_info['data_analysis'] = [
            'records_last_hour' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)"),
            'records_last_24h' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)"),
            'records_last_week' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)"),
            'unique_ips_last_hour' => $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)"),
            'unique_ips_last_24h' => $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)"),
            'bot_detection_working' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 1") > 0,
            'suspicious_patterns' => []
        ];

        // Check for suspicious patterns
        $high_frequency_ips = $wpdb->get_results(
            "SELECT ip_address, COUNT(*) as request_count,
             MIN(created_at) as first_request, MAX(created_at) as last_request
             FROM $table
             WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
             GROUP BY ip_address
             HAVING COUNT(*) > 10
             ORDER BY request_count DESC
             LIMIT 10"
        );

        $debug_info['data_analysis']['high_frequency_ips'] = $high_frequency_ips;

        // Check event type distribution
        $debug_info['data_analysis']['event_distribution'] = $wpdb->get_results(
            "SELECT event_type, COUNT(*) as count, COUNT(DISTINCT ip_address) as unique_ips
             FROM $table
             WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
             GROUP BY event_type"
        );

        // Check user agent patterns
        $debug_info['data_analysis']['user_agent_analysis'] = $wpdb->get_results(
            "SELECT
                CASE
                    WHEN user_agent LIKE '%bot%' OR user_agent LIKE '%crawler%' OR user_agent LIKE '%spider%' THEN 'Likely Bot'
                    WHEN user_agent = '' OR user_agent IS NULL THEN 'Empty UA'
                    WHEN LENGTH(user_agent) < 20 THEN 'Short UA'
                    ELSE 'Normal UA'
                END as ua_category,
                COUNT(*) as count,
                COUNT(DISTINCT ip_address) as unique_ips
             FROM $table
             WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
             GROUP BY ua_category"
        );

        // Check if bot detection columns exist and are being used
        if (in_array('is_bot', $debug_info['table_columns'])) {
            $debug_info['data_analysis']['bot_detection_stats'] = [
                'total_flagged_as_bot' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 1"),
                'total_flagged_as_human' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 0"),
                'unflagged_records' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot IS NULL"),
                'bot_types' => $wpdb->get_results("SELECT bot_type, COUNT(*) as count FROM $table WHERE is_bot = 1 GROUP BY bot_type")
            ];
        }
    }

    wp_send_json_success([
        'message' => 'Analytics debug completed',
        'debug_info' => $debug_info,
        'timestamp' => current_time('mysql')
    ]);
});

// AJAX endpoint to clean suspicious analytics data
add_action('wp_ajax_gotham_clean_analytics_data', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    $cleanup_results = [];

    // 1. Identify and remove obvious bot traffic that wasn't caught
    $bot_patterns = [
        "user_agent LIKE '%bot%'",
        "user_agent LIKE '%crawler%'",
        "user_agent LIKE '%spider%'",
        "user_agent LIKE '%scraper%'",
        "user_agent LIKE '%python%'",
        "user_agent LIKE '%curl%'",
        "user_agent LIKE '%wget%'",
        "user_agent = '' OR user_agent IS NULL",
        "LENGTH(user_agent) < 10",
        "browser = 'Other' OR browser = 'Unknown'"
    ];

    foreach ($bot_patterns as $pattern) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE ($pattern) AND (is_bot = 0 OR is_bot IS NULL)");
        if ($count > 0) {
            $updated = $wpdb->query("UPDATE $table SET is_bot = 1, bot_type = 'retroactive_detection' WHERE ($pattern) AND (is_bot = 0 OR is_bot IS NULL)");
            $cleanup_results[] = "Marked $updated records as bots: $pattern";
        }
    }

    // 2. Remove high-frequency spam from single IPs
    $spam_ips = $wpdb->get_results(
        "SELECT ip_address, COUNT(*) as count
         FROM $table
         WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
         GROUP BY ip_address
         HAVING COUNT(*) > 100"
    );

    foreach ($spam_ips as $spam_ip) {
        $updated = $wpdb->query($wpdb->prepare(
            "UPDATE $table SET is_bot = 1, bot_type = 'high_frequency_spam' WHERE ip_address = %s",
            $spam_ip->ip_address
        ));
        $cleanup_results[] = "Marked {$spam_ip->count} records from IP {$spam_ip->ip_address} as bot traffic";
    }

    // 3. Remove duplicate events from same IP within short timeframes
    $duplicates_removed = $wpdb->query(
        "DELETE t1 FROM $table t1
         INNER JOIN $table t2
         WHERE t1.id > t2.id
         AND t1.ip_address = t2.ip_address
         AND t1.event_type = t2.event_type
         AND t1.created_at BETWEEN t2.created_at AND DATE_ADD(t2.created_at, INTERVAL 10 SECOND)"
    );

    if ($duplicates_removed > 0) {
        $cleanup_results[] = "Removed $duplicates_removed duplicate events";
    }

    // 4. Update statistics
    $final_stats = [
        'total_records' => $wpdb->get_var("SELECT COUNT(*) FROM $table"),
        'human_records' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 0"),
        'bot_records' => $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE is_bot = 1"),
        'unique_human_ips' => $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE is_bot = 0"),
        'unique_bot_ips' => $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $table WHERE is_bot = 1")
    ];

    wp_send_json_success([
        'message' => 'Analytics data cleanup completed',
        'cleanup_results' => $cleanup_results,
        'final_stats' => $final_stats,
        'timestamp' => current_time('mysql')
    ]);
});

// AJAX endpoint to migrate analytics table structure
add_action('wp_ajax_gotham_migrate_analytics_table', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check current table structure
    $columns = $wpdb->get_results("DESCRIBE $table");
    $column_names = array_column($columns, 'Field');

    $migrations_run = [];
    $errors = [];

    // Add missing columns
    if (!in_array('is_bot', $column_names)) {
        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN is_bot tinyint(1) DEFAULT 0");
        if ($result !== false) {
            $migrations_run[] = 'Added is_bot column';
        } else {
            $errors[] = 'Failed to add is_bot column: ' . $wpdb->last_error;
        }
    }

    if (!in_array('bot_type', $column_names)) {
        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN bot_type varchar(50) DEFAULT ''");
        if ($result !== false) {
            $migrations_run[] = 'Added bot_type column';
        } else {
            $errors[] = 'Failed to add bot_type column: ' . $wpdb->last_error;
        }
    }

    if (!in_array('bot_confidence', $column_names)) {
        $result = $wpdb->query("ALTER TABLE $table ADD COLUMN bot_confidence int(3) DEFAULT 0");
        if ($result !== false) {
            $migrations_run[] = 'Added bot_confidence column';
        } else {
            $errors[] = 'Failed to add bot_confidence column: ' . $wpdb->last_error;
        }
    }

    // Add indexes for better performance
    $indexes_to_add = [
        'is_bot' => "ALTER TABLE $table ADD INDEX idx_is_bot (is_bot)",
        'human_traffic' => "ALTER TABLE $table ADD INDEX idx_human_traffic (is_bot, event_type, created_at)"
    ];

    foreach ($indexes_to_add as $index_name => $sql) {
        // Check if index exists
        $index_exists = $wpdb->get_var("SHOW INDEX FROM $table WHERE Key_name = 'idx_$index_name'");
        if (!$index_exists) {
            $result = $wpdb->query($sql);
            if ($result !== false) {
                $migrations_run[] = "Added index: $index_name";
            } else {
                $errors[] = "Failed to add index $index_name: " . $wpdb->last_error;
            }
        }
    }

    if (empty($migrations_run) && empty($errors)) {
        wp_send_json_success([
            'message' => 'Table structure is already up to date',
            'migrations_run' => [],
            'errors' => []
        ]);
    } else {
        wp_send_json_success([
            'message' => 'Table migration completed',
            'migrations_run' => $migrations_run,
            'errors' => $errors
        ]);
    }
});

// AJAX handler to manually recreate analytics table
add_action('wp_ajax_gotham_recreate_analytics_table', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Drop existing table
    $drop_result = $wpdb->query("DROP TABLE IF EXISTS $table");

    // Create table with complete schema including bot detection
    $charset_collate = $wpdb->get_charset_collate();
    $create_sql = "CREATE TABLE $table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        browser varchar(100),
        browser_version varchar(50) DEFAULT '',
        os varchar(50),
        country varchar(100),
        status varchar(20) DEFAULT 'pending',
        user_type varchar(20) DEFAULT 'unknown',
        session_id varchar(100) DEFAULT '',
        referrer_url text,
        page_url text,
        session_duration int(11) DEFAULT 0,
        is_mobile tinyint(1) DEFAULT 0,
        is_bot tinyint(1) DEFAULT 0,
        bot_type varchar(50) DEFAULT '',
        bot_confidence int(3) DEFAULT 0,
        screen_resolution varchar(20) DEFAULT '',
        timezone varchar(50) DEFAULT '',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY ip_address (ip_address),
        KEY event_type (event_type),
        KEY created_at (created_at),
        KEY is_bot (is_bot),
        KEY human_traffic (is_bot, event_type, created_at)
    ) $charset_collate;";

    $create_result = $wpdb->query($create_sql);

    // Verify creation
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") == $table) {
        wp_send_json_success([
            'message' => 'Analytics table recreated successfully',
            'debug' => [
                'drop_result' => $drop_result,
                'create_result' => $create_result,
                'table_name' => $table
            ]
        ]);
    } else {
        wp_send_json_error([
            'message' => 'Failed to recreate analytics table',
            'debug' => [
                'drop_result' => $drop_result,
                'create_result' => $create_result,
                'wpdb_error' => $wpdb->last_error,
                'table_name' => $table
            ]
        ]);
    }
});

// Add admin notice for table recreation if needed
add_action('admin_notices', function() {
    if (!current_user_can('manage_options')) return;

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if table exists and has correct structure
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") == $table) {
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
        $column_names = array();
        foreach ($columns as $column) {
            $column_names[] = $column->Field;
        }

        $required_columns = ['ip_address', 'event_type', 'browser', 'browser_version', 'os', 'country', 'status', 'user_type', 'is_bot', 'bot_type', 'bot_confidence'];
        $missing_columns = array_diff($required_columns, $column_names);

        if (!empty($missing_columns)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>Gotham Block Analytics:</strong> The analytics table needs to be updated. ';
            echo '<button type="button" class="button" onclick="recreateAnalyticsTable()">Fix Analytics Table</button></p>';
            echo '</div>';

            echo '<script>
            function recreateAnalyticsTable() {
                if (confirm("This will recreate the analytics table. All existing analytics data will be lost. Continue?")) {
                    fetch("' . admin_url('admin-ajax.php') . '", {
                        method: "POST",
                        headers: {"Content-Type": "application/x-www-form-urlencoded"},
                        body: "action=gotham_recreate_analytics_table&_wpnonce=' . wp_create_nonce('gotham_recreate_table') . '"
                    })
                    .then(res => res.json())
                    .then(data => {
                        if (data.success) {
                            alert("Analytics table recreated successfully!");
                            location.reload();
                        } else {
                            alert("Failed to recreate table: " + data.data.message);
                        }
                    });
                }
            }
            </script>';
        }
    }
});

// Load analytics admin module
if (is_admin()) {
    require_once plugin_dir_path(__FILE__) . 'admin/class-gotham-analytics.php';
}

// Generate or retrieve session ID for user tracking
function gotham_get_session_id() {
    if (isset($_COOKIE['gotham_session_id'])) {
        return sanitize_text_field($_COOKIE['gotham_session_id']);
    }

    $session_id = 'gs_' . uniqid() . '_' . time();
    setcookie('gotham_session_id', $session_id, time() + (24 * 60 * 60), '/'); // 24 hours
    return $session_id;
}

// Classify user type based on previous interactions
function gotham_classify_user($ip_address) {
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if user has been seen before
    $previous_visits = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s",
        $ip_address
    ));

    if ($previous_visits == 0) {
        return 'unique'; // First time visitor
    }

    // Check if user has converted before
    $conversions = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND event_type = 'adblock_disabled'",
        $ip_address
    ));

    if ($conversions > 0) {
        return 'converted'; // Previously converted user
    }

    // Check if user has declined before
    $declined = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE ip_address = %s AND event_type = 'popup_closed'",
        $ip_address
    ));

    if ($declined > 0) {
        return 'returning_declined'; // Returning user who previously declined
    }

    return 'returning'; // Returning user
}

// Get additional tracking data
function gotham_get_tracking_data() {
    return [
        'referrer_url' => $_SERVER['HTTP_REFERER'] ?? '',
        'page_url' => $_SERVER['REQUEST_URI'] ?? '',
        'screen_resolution' => '', // Will be filled by JavaScript
        'timezone' => '', // Will be filled by JavaScript
        'session_duration' => 0 // Will be calculated
    ];
}

// Enhanced analytics tracking with improved user tracking
function gotham_adblock_track_event($event_type = 'pop_displayed', $status = 'pending') {
    global $wpdb;

    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    if (empty($ip)) {
        return false;
    }

    $browser_info = gotham_get_browser_info($user_agent);
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $user_type = gotham_classify_user($ip);
    $session_id = gotham_get_session_id();
    $tracking_data = gotham_get_tracking_data();
    $now = current_time('mysql');
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Improved deduplication logic based on event type
    if ($event_type === 'pop_displayed') {
        // For popup displays, check if we already tracked this in the last hour
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $recent = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND event_type = %s AND created_at > %s ORDER BY id DESC LIMIT 1",
            $ip, $event_type, $one_hour_ago
        ));

        if ($recent) {
            // Update the existing recent record instead of creating a new one
            $wpdb->update(
                $table,
                array(
                    'updated_at' => $now,
                    'user_agent' => $user_agent,
                    'browser' => $browser_info['name'],
                    'browser_version' => $browser_info['version'],
                    'os' => $os,
                    'country' => $country,
                    'user_type' => $user_type,
                    'session_id' => $session_id,
                    'referrer_url' => $tracking_data['referrer_url'],
                    'page_url' => $tracking_data['page_url'],
                    'is_mobile' => $browser_info['is_mobile'] ? 1 : 0
                ),
                array('id' => $recent)
            );
            return $recent;
        }
    } elseif ($event_type === 'adblock_disabled') {
        // For conversions, check if we already have one today
        $today = date('Y-m-d');
        $existing_conversion = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND event_type = %s AND DATE(created_at) = %s",
            $ip, $event_type, $today
        ));

        if ($existing_conversion) {
            return $existing_conversion; // Don't create duplicate conversions
        }

        // Update any pending popup records for this IP to 'accepted' status
        $wpdb->update(
            $table,
            array('status' => 'accepted', 'updated_at' => $now),
            array('ip_address' => $ip, 'status' => 'pending')
        );
    } elseif ($event_type === 'popup_closed') {
        // Update any pending popup records for this IP to 'declined' status
        $wpdb->update(
            $table,
            array('status' => 'declined', 'updated_at' => $now),
            array('ip_address' => $ip, 'status' => 'pending')
        );
    }

    // Insert new record with enhanced data
    $result = $wpdb->insert(
        $table,
        array(
            'event_type' => $event_type,
            'ip_address' => $ip,
            'user_agent' => $user_agent,
            'browser' => $browser_info['name'],
            'browser_version' => $browser_info['version'],
            'os' => $os,
            'country' => $country,
            'status' => $status,
            'user_type' => $user_type,
            'session_id' => $session_id,
            'referrer_url' => $tracking_data['referrer_url'],
            'page_url' => $tracking_data['page_url'],
            'session_duration' => $tracking_data['session_duration'],
            'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
            'screen_resolution' => $tracking_data['screen_resolution'],
            'timezone' => $tracking_data['timezone'],
            'created_at' => $now,
            'updated_at' => $now
        )
    );

    return $result ? $wpdb->insert_id : false;
}

// Helper function to get OS from user agent
function gotham_get_os($user_agent) {
    $os_platform = 'Unknown OS';
    $os_array = array(
        '/windows nt 10/i'      =>  'Windows 10',
        '/windows nt 6.3/i'     =>  'Windows 8.1',
        '/windows nt 6.2/i'     =>  'Windows 8',
        '/windows nt 6.1/i'     =>  'Windows 7',
        '/windows nt 6.0/i'     =>  'Windows Vista',
        '/windows nt 5.2/i'     =>  'Windows Server 2003/XP x64',
        '/windows nt 5.1/i'     =>  'Windows XP',
        '/windows xp/i'         =>  'Windows XP',
        '/windows nt 5.0/i'     =>  'Windows 2000',
        '/windows me/i'         =>  'Windows ME',
        '/win98/i'              =>  'Windows 98',
        '/win95/i'              =>  'Windows 95',
        '/win16/i'              =>  'Windows 3.11',
        '/macintosh|mac os x/i' =>  'Mac OS X',
        '/mac_powerpc/i'        =>  'Mac OS 9',
        '/linux/i'              =>  'Linux',
        '/ubuntu/i'             =>  'Ubuntu',
        '/iphone/i'             =>  'iPhone',
        '/ipod/i'               =>  'iPod',
        '/ipad/i'               =>  'iPad',
        '/android/i'            =>  'Android',
        '/blackberry/i'         =>  'BlackBerry',
        '/webos/i'              =>  'Mobile'
    );

    foreach ($os_array as $regex => $value) {
        if (preg_match($regex, $user_agent)) {
            $os_platform = $value;
            break;
        }
    }
    return $os_platform;
}

// Integrate conversion tracking into popup close logic
// This will call the analytics tracker when the user clicks the CTA button to confirm disabling adblock
add_action('wp_footer', function() {
    ?>
    <script>
    // Enhanced conversion tracking with adblock verification
    document.addEventListener('DOMContentLoaded', function() {
        var btn = document.getElementById('gtab_mehn');
        if (btn) {
            btn.addEventListener('click', function() {
                // Set a flag to check for adblock status after a delay
                setTimeout(function() {
                    // Re-run the adblock detection after user claims to have disabled it
                    if (typeof gothamBatAdblock === 'function') {
                        var stillBlocked = gothamBatAdblock();
                        if (!stillBlocked) {
                            // Adblock is actually disabled, track conversion
                            if (typeof window.gotham_adblock_conversion === 'function') {
                                window.gotham_adblock_conversion();
                            }
                        }
                    }
                }, 2000); // Wait 2 seconds for user to disable adblock
            });
        }
    });
    </script>
    <?php
});

// AJAX handler for tracking adblock events - DEPRECATED: Use new class-based system
add_action('wp_ajax_nopriv_gotham_adblock_track_event', 'gotham_ajax_track_event');
add_action('wp_ajax_gotham_adblock_track_event', 'gotham_ajax_track_event');
function gotham_ajax_track_event() {
    // Use new secure analytics system if available
    if (class_exists('Gotham_Analytics')) {
        $plugin = Gotham_Block_Plugin::get_instance();
        $analytics = $plugin->get_analytics();
        $analytics->handle_track_event();
        return;
    }

    // Fallback to legacy system with enhanced security
    $received_nonce = $_POST['nonce'] ?? '';
    if (!wp_verify_nonce($received_nonce, 'gotham_adblock_nonce')) {
        wp_send_json_error(['message' => 'Invalid nonce']);
        return;
    }

    // Rate limiting
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $rate_limit_key = 'gotham_rate_limit_' . md5($ip);
    $current_count = get_transient($rate_limit_key);

    if ($current_count && $current_count >= 60) { // 60 requests per hour
        wp_send_json_error(['message' => 'Rate limit exceeded']);
        return;
    }

    set_transient($rate_limit_key, ($current_count ?: 0) + 1, HOUR_IN_SECONDS);

    // Perform bot detection
    $bot_detection = gotham_detect_bot();

    // Log bot detection for monitoring
    if ($bot_detection['is_bot']) {
        error_log('Gotham Analytics: Bot detected - ' . $bot_detection['reason'] . ' - UA: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown'));

        // Track bot traffic separately for detailed analysis
        gotham_track_bot_traffic($bot_detection);
    }

    // Continue to track ALL traffic (both human and bot) in main analytics table
    // Bot detection results will be stored in is_bot column for filtering

    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';

    // Check if table exists and has correct structure
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
        // Try to create the table
        gotham_create_analytics_table();

        // Check again
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            wp_send_json_error(['message' => 'Analytics table does not exist and could not be created', 'debug' => 'Table: ' . $table]);
            return;
        }
    } else {
        // Table exists, but check if it has the required columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
        $column_names = array();
        foreach ($columns as $column) {
            $column_names[] = $column->Field;
        }

        // Check for required columns including bot detection
        $required_columns = ['ip_address', 'event_type', 'browser', 'browser_version', 'os', 'country', 'status', 'user_type', 'is_bot', 'bot_type', 'bot_confidence'];
        $missing_columns = array_diff($required_columns, $column_names);

        if (!empty($missing_columns)) {
            // Table structure is outdated, drop and recreate it
            $wpdb->query("DROP TABLE IF EXISTS $table");
            gotham_create_analytics_table();

            // Verify the table was recreated properly
            if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
                wp_send_json_error(['message' => 'Failed to recreate analytics table', 'debug' => 'Missing columns: ' . implode(', ', $missing_columns)]);
                return;
            }
        }
    }

    $event_type = sanitize_text_field($_POST['event_type'] ?? '');
    $status = sanitize_text_field($_POST['status'] ?? 'pending');
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Get additional client-side data
    $screen_resolution = sanitize_text_field($_POST['screen_resolution'] ?? '');
    $timezone = sanitize_text_field($_POST['timezone'] ?? '');
    $session_duration = intval($_POST['session_duration'] ?? 0);
    $page_url = esc_url_raw($_POST['page_url'] ?? '');
    $referrer_url = esc_url_raw($_POST['referrer_url'] ?? '');

    if (empty($event_type)) {
        wp_send_json_error(['message' => 'Event type is required']);
        return;
    }

    // IP-based deduplication logic
    $today = date('Y-m-d');

    // For popup_displayed, allow multiple per day but limit to once per hour
    if ($event_type === 'pop_displayed') {
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $recent = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND created_at > %s",
            $event_type, $ip, $one_hour_ago
        ));
        if ($recent > 0) {
            wp_send_json_success(['message' => 'Event already tracked recently']);
            return;
        }
    }
    // For adblock_disabled, only allow once per day per IP
    elseif ($event_type === 'adblock_disabled') {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND DATE(created_at) = %s",
            $event_type, $ip, $today
        ));
        if ($existing > 0) {
            wp_send_json_success(['message' => 'Conversion already tracked today for this IP']);
            return;
        }
    }

    // For button_clicked, only allow once per day per IP
    elseif ($event_type === 'button_clicked') {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE event_type = %s AND ip_address = %s AND DATE(created_at) = %s",
            $event_type, $ip, $today
        ));
        if ($existing > 0) {
            wp_send_json_success(['message' => 'Button click already tracked today for this IP']);
            return;
        }
    }

    // Get additional data
    $browser_info = gotham_get_browser_info($user_agent);
    $browser_type = gotham_get_browser_type($user_agent); // Get browser type without version for analytics
    $country = gotham_get_country_by_ip($ip);
    $os = gotham_get_os($user_agent);
    $user_type = gotham_classify_user($ip);
    $session_id = gotham_get_session_id();
    $now = current_time('mysql');

    // Override tracking data with client-side data if available
    $tracking_data = [
        'referrer_url' => $referrer_url ?: ($_SERVER['HTTP_REFERER'] ?? ''),
        'page_url' => $page_url ?: ($_SERVER['REQUEST_URI'] ?? ''),
        'screen_resolution' => $screen_resolution,
        'timezone' => $timezone,
        'session_duration' => $session_duration
    ];

    // For popup_displayed events, check if we should update existing pending record
    if ($event_type === 'pop_displayed') {
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $table WHERE ip_address = %s AND status = 'pending' ORDER BY id DESC LIMIT 1",
            $ip
        ));

        if ($existing) {
            // Prepare update data
            $update_data = array(
                'updated_at' => $now,
                'event_type' => $event_type,
                'user_agent' => $user_agent,
                'browser' => $browser_type, // Use browser type without version
                'browser_version' => $browser_info['version'],
                'os' => $os,
                'country' => $country,
                'user_type' => $user_type,
                'session_id' => $session_id,
                'referrer_url' => $tracking_data['referrer_url'],
                'page_url' => $tracking_data['page_url'],
                'session_duration' => $tracking_data['session_duration'],
                'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
                'screen_resolution' => $tracking_data['screen_resolution'],
                'timezone' => $tracking_data['timezone']
            );

            // Add bot detection fields if columns exist
            $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
            $column_names = array_column($columns, 'Field');

            if (in_array('is_bot', $column_names)) {
                $update_data['is_bot'] = $bot_detection['is_bot'] ? 1 : 0;
                $update_data['bot_type'] = $bot_detection['bot_type'] ?? '';
                $update_data['bot_confidence'] = $bot_detection['confidence'] ?? 0;
            }

            // Update existing record
            $result = $wpdb->update($table, $update_data, array('id' => $existing->id));
            wp_send_json_success(['message' => 'Event updated', 'id' => $existing->id]);
            return;
        }
    }

    // Insert new record with enhanced data including bot detection
    $insert_data = array(
        'event_type' => $event_type,
        'ip_address' => $ip,
        'user_agent' => $user_agent,
        'browser' => $browser_type, // Use browser type without version for analytics grouping
        'browser_version' => $browser_info['version'], // Keep version for detailed analysis if needed
        'os' => $os,
        'country' => $country,
        'status' => $status,
        'user_type' => $user_type,
        'session_id' => $session_id,
        'referrer_url' => $tracking_data['referrer_url'],
        'page_url' => $tracking_data['page_url'],
        'session_duration' => $tracking_data['session_duration'],
        'is_mobile' => $browser_info['is_mobile'] ? 1 : 0,
        'screen_resolution' => $tracking_data['screen_resolution'],
        'timezone' => $tracking_data['timezone'],
        'created_at' => $now,
        'updated_at' => $now
    );

    // Add bot detection fields if columns exist
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table");
    $column_names = array_column($columns, 'Field');

    if (in_array('is_bot', $column_names)) {
        // Store actual bot detection results
        $insert_data['is_bot'] = $bot_detection['is_bot'] ? 1 : 0;
        $insert_data['bot_type'] = $bot_detection['bot_type'] ?? '';
        $insert_data['bot_confidence'] = $bot_detection['confidence'] ?? 0;
    }

    $result = $wpdb->insert($table, $insert_data);

    if ($result === false) {
        error_log('Gotham Analytics: Database insert failed - ' . $wpdb->last_error);
        wp_send_json_error([
            'message' => 'Failed to insert event',
            'debug' => [
                'wpdb_error' => $wpdb->last_error,
                'event_type' => $event_type,
                'ip' => $ip,
                'table' => $table,
                'insert_data' => [
                    'event_type' => $event_type,
                    'ip_address' => $ip,
                    'browser' => $browser_info['name'],
                    'status' => $status
                ]
            ]
        ]);
    } else {
        error_log('Gotham Analytics: Event tracked successfully - ID: ' . $wpdb->insert_id . ', Event: ' . $event_type);
        wp_send_json_success([
            'message' => 'Event tracked successfully',
            'id' => $wpdb->insert_id,
            'event_type' => $event_type,
            'debug' => [
                'ip' => $ip,
                'user_type' => $user_type,
                'browser' => $browser_info['name'],
                'table' => $table
            ]
        ]);
    }
}

// AJAX handler for the legacy action name (for backward compatibility)
add_action('wp_ajax_nopriv_gotham_track_adblock_action', 'gotham_ajax_track_legacy_action');
add_action('wp_ajax_gotham_track_adblock_action', 'gotham_ajax_track_legacy_action');
function gotham_ajax_track_legacy_action() {
    // Map legacy action to new handler
    $_POST['event_type'] = $_POST['event_type'] ?? 'popup_closed';
    gotham_ajax_track_event();
}

// Enhanced browser detection with version information
function gotham_get_browser_info($user_agent) {
    if (empty($user_agent)) return ['name' => 'Unknown', 'version' => '', 'is_mobile' => false];

    $user_agent_lower = strtolower($user_agent);
    $is_mobile = (strpos($user_agent_lower, 'mobile') !== false ||
                  strpos($user_agent_lower, 'android') !== false ||
                  strpos($user_agent_lower, 'iphone') !== false ||
                  strpos($user_agent_lower, 'ipad') !== false);

    // Edge detection
    if (preg_match('/edg\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Microsoft Edge', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/edge\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Microsoft Edge Legacy', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Opera detection
    if (preg_match('/opr\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Opera', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/opera\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Opera', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Brave detection
    if (strpos($user_agent_lower, 'brave') !== false) {
        if (preg_match('/chrome\/([0-9.]+)/i', $user_agent, $matches)) {
            return ['name' => 'Brave', 'version' => $matches[1], 'is_mobile' => $is_mobile];
        }
        return ['name' => 'Brave', 'version' => '', 'is_mobile' => $is_mobile];
    }

    // Vivaldi detection
    if (preg_match('/vivaldi\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Vivaldi', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Firefox detection
    if (preg_match('/firefox\/([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Firefox', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Chrome detection (must be after other Chromium-based browsers)
    if (preg_match('/chrome\/([0-9.]+)/i', $user_agent, $matches) && strpos($user_agent_lower, 'safari') !== false) {
        return ['name' => 'Chrome', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Safari detection
    if (preg_match('/version\/([0-9.]+).*safari/i', $user_agent, $matches) && strpos($user_agent_lower, 'chrome') === false) {
        return ['name' => 'Safari', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    // Internet Explorer detection
    if (preg_match('/msie ([0-9.]+)/i', $user_agent, $matches)) {
        return ['name' => 'Internet Explorer', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }
    if (preg_match('/rv:([0-9.]+).*trident/i', $user_agent, $matches)) {
        return ['name' => 'Internet Explorer', 'version' => $matches[1], 'is_mobile' => $is_mobile];
    }

    return ['name' => 'Other', 'version' => '', 'is_mobile' => $is_mobile];
}

// Backward compatibility function
function gotham_get_browser($user_agent) {
    $browser_info = gotham_get_browser_info($user_agent);
    return $browser_info['name'];
}

// Get browser name without version for analytics grouping
function gotham_get_browser_type($user_agent) {
    if (empty($user_agent)) return 'Unknown';

    $user_agent_lower = strtolower($user_agent);

    // Edge detection (check first as it contains "chrome" in user agent)
    if (strpos($user_agent_lower, 'edg/') !== false || strpos($user_agent_lower, 'edge/') !== false) {
        return 'Microsoft Edge';
    }

    // Opera detection (check before Chrome as it contains "chrome" in user agent)
    if (strpos($user_agent_lower, 'opr/') !== false || strpos($user_agent_lower, 'opera/') !== false) {
        return 'Opera';
    }

    // Brave detection (check before Chrome as it's Chromium-based)
    if (strpos($user_agent_lower, 'brave') !== false) {
        return 'Brave';
    }

    // Vivaldi detection (check before Chrome as it's Chromium-based)
    if (strpos($user_agent_lower, 'vivaldi/') !== false) {
        return 'Vivaldi';
    }

    // Firefox detection
    if (strpos($user_agent_lower, 'firefox/') !== false) {
        return 'Firefox';
    }

    // Chrome detection (must be after other Chromium-based browsers)
    if (strpos($user_agent_lower, 'chrome/') !== false && strpos($user_agent_lower, 'safari') !== false) {
        return 'Chrome';
    }

    // Safari detection (check after Chrome to avoid false positives)
    if (strpos($user_agent_lower, 'safari') !== false && strpos($user_agent_lower, 'chrome') === false) {
        return 'Safari';
    }

    // Internet Explorer detection
    if (strpos($user_agent_lower, 'msie') !== false || strpos($user_agent_lower, 'trident') !== false) {
        return 'Internet Explorer';
    }

    // Samsung Internet
    if (strpos($user_agent_lower, 'samsungbrowser') !== false) {
        return 'Samsung Internet';
    }

    // UC Browser
    if (strpos($user_agent_lower, 'ucbrowser') !== false) {
        return 'UC Browser';
    }

    // Mobile browsers
    if (strpos($user_agent_lower, 'mobile') !== false) {
        if (strpos($user_agent_lower, 'android') !== false) {
            return 'Android Browser';
        }
        return 'Mobile Browser';
    }

    return 'Other';
}

// Enhanced country detection by IP with caching and multiple fallbacks
function gotham_get_country_by_ip($ip) {
    if (empty($ip) || $ip === '127.0.0.1' || $ip === '::1' || filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        return 'Local';
    }

    // Check cache first (transient for 24 hours)
    $cache_key = 'gotham_country_' . md5($ip);
    $cached_country = get_transient($cache_key);
    if ($cached_country !== false) {
        return $cached_country;
    }

    $country = 'Unknown';
    $apis = [
        'https://ipapi.co/' . $ip . '/country_name/',
        'http://ip-api.com/line/' . $ip . '?fields=country',
        'https://ipinfo.io/' . $ip . '/country'
    ];

    foreach ($apis as $api_url) {
        $response = wp_remote_get($api_url, [
            'timeout' => 3,
            'user-agent' => 'WordPress/Gotham-Block-Plugin',
            'sslverify' => false // For compatibility with some servers
        ]);

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $result = trim(wp_remote_retrieve_body($response));

            // Validate the result
            if (!empty($result) &&
                $result !== 'Undefined' &&
                $result !== 'fail' &&
                $result !== 'error' &&
                strlen($result) > 1 &&
                strlen($result) < 100) {

                $country = $result;
                break;
            }
        }

        // Small delay between API calls to be respectful
        usleep(100000); // 0.1 seconds
    }

    // Cache the result (even if Unknown) to avoid repeated API calls
    $cache_duration = ($country === 'Unknown') ? HOUR_IN_SECONDS : 24 * HOUR_IN_SECONDS;
    set_transient($cache_key, $country, $cache_duration);

    return $country;
}





// Remove any custom <script> tags or event dispatches added for analytics tracking
?>