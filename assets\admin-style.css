/**
 * Gotham Block Admin Styles
 * Modern admin interface styling
 */

/* Admin Dashboard */
.gotham-analytics-summary {
    margin: 20px 0;
}

.gotham-stat-boxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gotham-stat-box {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.gotham-stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.gotham-stat-box h3 {
    margin: 0 0 10px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.gotham-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #2271b1;
    margin-bottom: 5px;
}

.gotham-stat-label {
    font-size: 12px;
    color: #999;
}

/* Analytics Filters */
.gotham-analytics-filters {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.gotham-analytics-filters h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.gotham-analytics-filters form {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.gotham-analytics-filters label {
    font-weight: 600;
    color: #555;
}

.gotham-analytics-filters input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Charts */
.gotham-analytics-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gotham-chart-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gotham-chart-container canvas {
    max-width: 100%;
    height: auto;
}

/* Admin Actions */
.gotham-admin-actions {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.gotham-admin-actions h2 {
    margin-top: 0;
    color: #333;
}

.gotham-admin-actions p {
    margin-bottom: 15px;
}

.gotham-admin-actions .description {
    color: #666;
    font-style: italic;
    margin-left: 10px;
}

/* Plugin Information */
.gotham-admin-info {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.gotham-admin-info h2 {
    margin-top: 0;
    color: #333;
}

.gotham-admin-info .form-table {
    margin-top: 15px;
}

.gotham-admin-info .form-table th {
    font-weight: 600;
    color: #555;
}

.gotham-admin-info .dashicons {
    margin-right: 5px;
}

/* Settings Form */
.form-table th {
    font-weight: 600;
    color: #555;
}

.form-table .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Buttons */
.button.gotham-button {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    text-decoration: none;
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.button.gotham-button:hover {
    background: #135e96;
    border-color: #135e96;
    color: #fff;
}

.button.gotham-button:focus {
    box-shadow: 0 0 0 2px #2271b1;
}

/* Loading States */
.gotham-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.gotham-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: gotham-spin 1s linear infinite;
}

@keyframes gotham-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.gotham-notice {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.gotham-notice.success {
    background: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.gotham-notice.error {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.gotham-notice.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.gotham-notice.info {
    background: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* Data Tables */
.gotham-data-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gotham-data-table th,
.gotham-data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.gotham-data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.gotham-data-table tr:hover {
    background: #f8f9fa;
}

.gotham-data-table .number {
    text-align: right;
    font-family: monospace;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gotham-analytics-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .gotham-stat-boxes {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .gotham-stat-box {
        padding: 15px;
    }
    
    .gotham-stat-number {
        font-size: 24px;
    }
    
    .gotham-analytics-filters form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .gotham-analytics-filters input[type="date"] {
        width: 100%;
    }
    
    .gotham-chart-container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .gotham-stat-boxes {
        grid-template-columns: 1fr;
    }
    
    .gotham-admin-actions,
    .gotham-admin-info,
    .gotham-analytics-filters {
        padding: 15px;
    }
    
    .gotham-data-table {
        font-size: 14px;
    }
    
    .gotham-data-table th,
    .gotham-data-table td {
        padding: 8px 10px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .gotham-stat-box,
    .gotham-analytics-filters,
    .gotham-chart-container,
    .gotham-admin-actions,
    .gotham-admin-info {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .gotham-stat-box h3,
    .gotham-admin-info h2,
    .gotham-analytics-filters h2,
    .gotham-admin-actions h2 {
        color: #f7fafc;
    }
    
    .gotham-stat-number {
        color: #63b3ed;
    }
    
    .gotham-data-table {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .gotham-data-table th {
        background: #4a5568;
        color: #f7fafc;
    }
    
    .gotham-data-table tr:hover {
        background: #4a5568;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .gotham-stat-box,
    .gotham-analytics-filters,
    .gotham-chart-container,
    .gotham-admin-actions,
    .gotham-admin-info {
        border-width: 2px;
    }
    
    .button.gotham-button {
        border-width: 2px;
    }
}

/* Print Styles */
@media print {
    .gotham-admin-actions,
    .gotham-analytics-filters form {
        display: none;
    }
    
    .gotham-stat-boxes,
    .gotham-analytics-charts {
        break-inside: avoid;
    }
    
    .gotham-chart-container {
        box-shadow: none;
        border: 1px solid #000;
    }
}
