<?php
/**
 * Gotham Block Popup Class
 * Handles popup display and functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Popup {
    
    private $analytics;
    private $security;
    
    public function __construct($analytics) {
        $this->analytics = $analytics;
        $this->security = new Gotham_Security();
    }
    
    /**
     * Initialize popup functionality
     */
    public function init() {
        // Only load on frontend
        if (is_admin()) {
            return;
        }
        
        // Check if popup should be displayed
        if ($this->should_display_popup()) {
            add_action('wp_footer', array($this, 'render_popup'));
            add_action('wp_enqueue_scripts', array($this, 'enqueue_popup_assets'));
        }
    }
    
    /**
     * Check if popup should be displayed
     */
    private function should_display_popup() {
        // Check if plugin is paused
        $fury_mode = get_option('gothamadblock_option_fury', 'ssj1');
        if ($fury_mode === 'paused') {
            return false;
        }
        
        // Check if user has already seen popup recently
        $cookie_time = get_option('gothamadblock_option_cookietime', '2592000');
        $cookie_name = 'gotham_adblock_seen';
        
        if (isset($_COOKIE[$cookie_name])) {
            return false;
        }
        
        // Check if this is a bot (simple check)
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if ($this->is_likely_bot($user_agent)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Simple bot detection for popup display
     */
    private function is_likely_bot($user_agent) {
        $bot_indicators = array('bot', 'crawler', 'spider', 'scraper', 'googlebot', 'bingbot');
        $user_agent_lower = strtolower($user_agent);
        
        foreach ($bot_indicators as $indicator) {
            if (strpos($user_agent_lower, $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Enqueue popup assets
     */
    public function enqueue_popup_assets() {
        // Enqueue popup CSS
        wp_enqueue_style(
            'gotham-popup-style',
            GOTHAM_BLOCK_PLUGIN_URL . 'assets/popup-style.css',
            array(),
            GOTHAM_BLOCK_VERSION
        );
        
        // Enqueue popup JavaScript
        wp_enqueue_script(
            'gotham-popup-script',
            GOTHAM_BLOCK_PLUGIN_URL . 'assets/popup-script.js',
            array('jquery'),
            GOTHAM_BLOCK_VERSION,
            true
        );
        
        // Localize script with settings
        $popup_settings = array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => $this->security->create_nonce(),
            'delay' => intval(get_option('gothamadblock_option_popup_delay', '0')),
            'cookieTime' => intval(get_option('gothamadblock_option_cookietime', '2592000')),
            'furyMode' => get_option('gothamadblock_option_fury', 'ssj1'),
            'debugMode' => defined('WP_DEBUG') && WP_DEBUG
        );
        
        wp_localize_script('gotham-popup-script', 'gothamPopup', $popup_settings);
    }
    
    /**
     * Render the popup HTML
     */
    public function render_popup() {
        $fury_mode = get_option('gothamadblock_option_fury', 'ssj1');
        $powered_by = get_option('gothamadblock_option_powered', 'non');
        
        // Get popup content based on fury mode
        $popup_content = $this->get_popup_content($fury_mode);
        
        ?>
        <div id="gotham-adblock-popup" class="gotham-popup-overlay" style="display: none;">
            <div class="gotham-popup-container gotham-fury-<?php echo esc_attr($fury_mode); ?>">
                <div class="gotham-popup-header">
                    <button type="button" class="gotham-popup-close" aria-label="Close">&times;</button>
                </div>
                
                <div class="gotham-popup-content">
                    <?php echo wp_kses_post($popup_content['message']); ?>
                </div>
                
                <div class="gotham-popup-actions">
                    <?php if (!empty($popup_content['buttons'])): ?>
                        <?php foreach ($popup_content['buttons'] as $button): ?>
                            <button type="button" 
                                    class="gotham-popup-button <?php echo esc_attr($button['class']); ?>"
                                    data-action="<?php echo esc_attr($button['action']); ?>">
                                <?php echo esc_html($button['text']); ?>
                            </button>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <?php if ($powered_by === 'oui'): ?>
                <div class="gotham-popup-footer">
                    <small>Powered by <a href="https://www.kapsulecorp.com/" target="_blank" rel="noopener">Gotham Block</a></small>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Adblock detection script -->
        <script type="text/javascript">
        (function() {
            // Create a test element that adblockers typically block
            var testAd = document.createElement('div');
            testAd.innerHTML = '&nbsp;';
            testAd.className = 'adsbox';
            testAd.style.position = 'absolute';
            testAd.style.left = '-10000px';
            testAd.style.top = '-10000px';
            testAd.style.width = '1px';
            testAd.style.height = '1px';
            document.body.appendChild(testAd);
            
            // Check if the element is blocked
            setTimeout(function() {
                var isBlocked = testAd.offsetHeight === 0 || 
                               testAd.offsetWidth === 0 || 
                               testAd.style.display === 'none' ||
                               testAd.style.visibility === 'hidden';
                
                document.body.removeChild(testAd);
                
                if (isBlocked && typeof window.gothamShowPopup === 'function') {
                    window.gothamShowPopup();
                }
            }, 100);
        })();
        </script>
        <?php
    }
    
    /**
     * Get popup content based on fury mode
     */
    private function get_popup_content($fury_mode) {
        $content = array();
        
        switch ($fury_mode) {
            case 'ssj1':
                $content['message'] = '<h3>' . __('Ad Blocker Detected', 'gotham-block') . '</h3>' .
                                    '<p>' . __('We noticed you\'re using an ad blocker. Our site relies on advertising revenue to provide free content. Please consider disabling your ad blocker for our site.', 'gotham-block') . '</p>';
                $content['buttons'] = array(
                    array(
                        'text' => __('I\'ll disable it', 'gotham-block'),
                        'action' => 'disable_adblock',
                        'class' => 'gotham-btn-primary'
                    ),
                    array(
                        'text' => __('Continue anyway', 'gotham-block'),
                        'action' => 'continue',
                        'class' => 'gotham-btn-secondary'
                    )
                );
                break;
                
            case 'ssj2':
                $content['message'] = '<h3>' . __('Please Support Our Site', 'gotham-block') . '</h3>' .
                                    '<p>' . __('Your ad blocker is preventing us from earning revenue that keeps our content free. We respect your choice, but please consider whitelisting our site or making a small donation.', 'gotham-block') . '</p>';
                $content['buttons'] = array(
                    array(
                        'text' => __('Whitelist this site', 'gotham-block'),
                        'action' => 'whitelist',
                        'class' => 'gotham-btn-primary'
                    ),
                    array(
                        'text' => __('Make a donation', 'gotham-block'),
                        'action' => 'donate',
                        'class' => 'gotham-btn-secondary'
                    ),
                    array(
                        'text' => __('No thanks', 'gotham-block'),
                        'action' => 'decline',
                        'class' => 'gotham-btn-tertiary'
                    )
                );
                break;
                
            case 'ssj3':
                $content['message'] = '<h3>' . __('Ad Blocker Impact', 'gotham-block') . '</h3>' .
                                    '<p>' . __('Ad blockers significantly impact our ability to maintain this website. Without advertising revenue, we cannot continue providing quality content. Please help us by disabling your ad blocker.', 'gotham-block') . '</p>' .
                                    '<p><strong>' . __('This site will be limited until ad blocker is disabled.', 'gotham-block') . '</strong></p>';
                $content['buttons'] = array(
                    array(
                        'text' => __('Disable Ad Blocker', 'gotham-block'),
                        'action' => 'disable_required',
                        'class' => 'gotham-btn-primary'
                    )
                );
                break;
                
            default:
                $content['message'] = '<h3>' . __('Ad Blocker Detected', 'gotham-block') . '</h3>' .
                                    '<p>' . __('Please disable your ad blocker to continue.', 'gotham-block') . '</p>';
                $content['buttons'] = array(
                    array(
                        'text' => __('Continue', 'gotham-block'),
                        'action' => 'continue',
                        'class' => 'gotham-btn-primary'
                    )
                );
        }
        
        return apply_filters('gotham_popup_content', $content, $fury_mode);
    }
    
    /**
     * Handle popup actions via AJAX
     */
    public function handle_popup_action() {
        // Verify nonce
        $nonce = sanitize_text_field($_POST['nonce'] ?? '');
        if (!$this->security->verify_nonce($nonce)) {
            wp_die('Security check failed', 'Security Error', array('response' => 403));
        }
        
        $action = sanitize_text_field($_POST['popup_action'] ?? '');
        $valid_actions = array('disable_adblock', 'continue', 'whitelist', 'donate', 'decline', 'disable_required');
        
        if (!in_array($action, $valid_actions)) {
            wp_send_json_error('Invalid action');
        }
        
        // Track the action
        $tracking_data = array(
            'event_type' => 'button_clicked',
            'status' => $action,
            'url' => $this->security->sanitize_url($_POST['url'] ?? ''),
            'referer' => $this->security->sanitize_url($_POST['referer'] ?? ''),
            'ip' => $this->security->get_client_ip(),
            'user_agent' => $this->security->sanitize_user_agent($_SERVER['HTTP_USER_AGENT'] ?? '')
        );
        
        $this->analytics->database->insert_analytics_data($tracking_data);
        
        // Set cookie to prevent showing popup again
        $cookie_time = intval(get_option('gothamadblock_option_cookietime', '2592000'));
        setcookie('gotham_adblock_seen', '1', time() + $cookie_time, '/');
        
        wp_send_json_success(array(
            'message' => 'Action recorded',
            'action' => $action
        ));
    }
    
    /**
     * Get popup statistics
     */
    public function get_popup_stats($filters = array()) {
        global $wpdb;
        
        $table_stats = $this->analytics->database->get_table_stats();
        $where_clauses = array("event_type = 'button_clicked'");
        $where_values = array();
        
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['end_date'];
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT status, COUNT(*) as count 
                FROM {$table_stats} 
                WHERE {$where_sql} 
                GROUP BY status 
                ORDER BY count DESC";
        
        if (!empty($where_values)) {
            $prepared_sql = $wpdb->prepare($sql, $where_values);
        } else {
            $prepared_sql = $sql;
        }
        
        $results = $wpdb->get_results($prepared_sql, ARRAY_A);
        
        $stats = array(
            'total_interactions' => 0,
            'actions' => array()
        );
        
        foreach ($results as $result) {
            $stats['total_interactions'] += $result['count'];
            $stats['actions'][$result['status']] = $result['count'];
        }
        
        return $stats;
    }
}
