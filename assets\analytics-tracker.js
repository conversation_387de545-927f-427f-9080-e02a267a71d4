/**
 * Gotham Block Analytics Tracker - Enhanced Version
 * Improved behavioral tracking, bot detection, and security
 */

(function($) {
    'use strict';

    // Configuration with fallbacks
    var config = {
        trackingEnabled: (window.gothamAnalytics && window.gothamAnalytics.trackingEnabled) || false,
        ajaxUrl: (window.gothamAnalytics && window.gothamAnalytics.ajaxUrl) || '',
        nonce: (window.gothamAnalytics && window.gothamAnalytics.nonce) || '',
        debugMode: (window.gothamAnalytics && window.gothamAnalytics.debugMode) || false,
        rateLimitDelay: 1000, // Minimum delay between requests
        maxEventsPerSession: 50 // Maximum events to prevent spam
    };

    // Rate limiting
    var lastRequestTime = 0;
    var eventCount = 0;

    // Enhanced behavioral tracking data
    var behaviorData = {
        mouseMovements: 0,
        keystrokes: 0,
        scrollEvents: 0,
        clickEvents: 0,
        focusEvents: 0,
        timeOnPage: 0,
        sessionStart: Date.now(),
        interactions: [],
        fingerprint: {},
        mousePattern: [],
        clickPattern: [],
        scrollPattern: []
    };

    // Security checks
    function isValidEnvironment() {
        // Check if we're in a valid browser environment
        if (typeof window === 'undefined' || typeof document === 'undefined') {
            return false;
        }

        // Check for basic bot indicators
        if (navigator.webdriver === true || 
            window.navigator.webdriver === true ||
            window.callPhantom !== undefined ||
            window._phantom !== undefined) {
            return false;
        }

        return true;
    }

    // Rate limiting check
    function canMakeRequest() {
        var now = Date.now();
        
        if (eventCount >= config.maxEventsPerSession) {
            if (config.debugMode) {
                console.warn('Gotham Analytics: Event limit reached for this session');
            }
            return false;
        }

        if (now - lastRequestTime < config.rateLimitDelay) {
            if (config.debugMode) {
                console.warn('Gotham Analytics: Rate limit exceeded');
            }
            return false;
        }

        return true;
    }

    // Initialize tracking with security checks
    function initTracking() {
        if (!config.trackingEnabled || !isValidEnvironment()) {
            return;
        }

        // Enhanced mouse movement tracking with pattern analysis
        var mouseThrottle = 0;
        $(document).on('mousemove', function(e) {
            if (Date.now() - mouseThrottle > 100) { // Throttle to every 100ms
                behaviorData.mouseMovements++;
                behaviorData.mousePattern.push({
                    x: e.clientX,
                    y: e.clientY,
                    timestamp: Date.now()
                });
                
                // Keep only last 10 movements for pattern analysis
                if (behaviorData.mousePattern.length > 10) {
                    behaviorData.mousePattern.shift();
                }
                mouseThrottle = Date.now();
            }
        });

        // Enhanced keystroke tracking
        $(document).on('keydown', function(e) {
            behaviorData.keystrokes++;
            
            // Don't track sensitive keys
            var sensitiveKeys = [8, 9, 13, 16, 17, 18, 27]; // Backspace, Tab, Enter, Shift, Ctrl, Alt, Esc
            if (sensitiveKeys.indexOf(e.keyCode) === -1) {
                behaviorData.interactions.push({
                    type: 'keystroke',
                    timestamp: Date.now(),
                    keyCode: e.keyCode
                });
            }
        });

        // Enhanced scroll tracking with pattern analysis
        var scrollThrottle = 0;
        $(window).on('scroll', function() {
            if (Date.now() - scrollThrottle > 200) { // Throttle to every 200ms
                behaviorData.scrollEvents++;
                behaviorData.scrollPattern.push({
                    scrollY: window.scrollY,
                    timestamp: Date.now()
                });
                
                // Keep only last 10 scroll events
                if (behaviorData.scrollPattern.length > 10) {
                    behaviorData.scrollPattern.shift();
                }
                scrollThrottle = Date.now();
            }
        });

        // Enhanced click tracking with timing analysis
        $(document).on('click', function(e) {
            var now = Date.now();
            behaviorData.clickEvents++;
            
            var clickData = {
                type: 'click',
                timestamp: now,
                target: e.target.tagName.toLowerCase(),
                x: e.clientX,
                y: e.clientY
            };
            
            // Calculate click timing if we have previous clicks
            if (behaviorData.clickPattern.length > 0) {
                var lastClick = behaviorData.clickPattern[behaviorData.clickPattern.length - 1];
                clickData.timeSinceLastClick = now - lastClick.timestamp;
            }
            
            behaviorData.clickPattern.push(clickData);
            behaviorData.interactions.push(clickData);
            
            // Keep only last 10 clicks
            if (behaviorData.clickPattern.length > 10) {
                behaviorData.clickPattern.shift();
            }
        });

        // Focus/blur tracking
        $(window).on('focus blur', function(e) {
            behaviorData.focusEvents++;
            behaviorData.interactions.push({
                type: e.type,
                timestamp: Date.now()
            });
        });

        // Generate comprehensive fingerprint
        generateFingerprint();

        // Track page visibility changes with enhanced detection
        if (typeof document.hidden !== "undefined") {
            $(document).on('visibilitychange', function() {
                var eventType = document.hidden ? 'page_hidden' : 'page_visible';
                trackEvent(eventType, 'tracking');
            });
        }

        // Update time on page and detect suspicious patterns
        setInterval(function() {
            behaviorData.timeOnPage = Math.floor((Date.now() - behaviorData.sessionStart) / 1000);
            
            // Detect suspicious patterns every 10 seconds
            if (behaviorData.timeOnPage % 10 === 0) {
                detectSuspiciousPatterns();
            }
        }, 1000);

        if (config.debugMode) {
            console.log('Gotham Analytics: Enhanced tracking initialized');
        }
    }

    // Enhanced fingerprint generation with more data points
    function generateFingerprint() {
        behaviorData.fingerprint = {
            userAgent: navigator.userAgent || '',
            language: navigator.language || '',
            languages: navigator.languages ? navigator.languages.join(',') : '',
            platform: navigator.platform || '',
            cookieEnabled: navigator.cookieEnabled || false,
            doNotTrack: navigator.doNotTrack || '',
            screenResolution: screen.width + 'x' + screen.height,
            availableScreenResolution: screen.availWidth + 'x' + screen.availHeight,
            colorDepth: screen.colorDepth || 0,
            pixelDepth: screen.pixelDepth || 0,
            timezone: getTimezone(),
            timezoneOffset: new Date().getTimezoneOffset(),
            pluginsCount: navigator.plugins ? navigator.plugins.length : 0,
            webdriver: navigator.webdriver || false,
            hardwareConcurrency: navigator.hardwareConcurrency || 0,
            deviceMemory: navigator.deviceMemory || 0,
            connection: getConnectionInfo()
        };

        // Enhanced canvas fingerprinting
        try {
            var canvas = document.createElement('canvas');
            var ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('Gotham fingerprint test 🔒', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Gotham fingerprint test 🔒', 4, 17);
            behaviorData.fingerprint.canvasFingerprint = canvas.toDataURL().substring(0, 100);
        } catch (e) {
            behaviorData.fingerprint.canvasFingerprint = 'blocked';
        }

        // Enhanced WebGL fingerprinting
        try {
            var gl = document.createElement('canvas').getContext('webgl') || 
                     document.createElement('canvas').getContext('experimental-webgl');
            if (gl) {
                var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    behaviorData.fingerprint.webglVendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || '';
                    behaviorData.fingerprint.webglRenderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || '';
                }
                behaviorData.fingerprint.webglVersion = gl.getParameter(gl.VERSION) || '';
                behaviorData.fingerprint.webglShadingLanguageVersion = gl.getParameter(gl.SHADING_LANGUAGE_VERSION) || '';
            }
        } catch (e) {
            behaviorData.fingerprint.webglVendor = 'unavailable';
            behaviorData.fingerprint.webglRenderer = 'unavailable';
        }

        // Audio context fingerprinting
        try {
            var audioContext = window.AudioContext || window.webkitAudioContext;
            if (audioContext) {
                var context = new audioContext();
                behaviorData.fingerprint.audioFingerprint = context.sampleRate + '_' + context.state;
                context.close();
            }
        } catch (e) {
            behaviorData.fingerprint.audioFingerprint = 'unavailable';
        }
    }

    // Helper functions for fingerprinting
    function getTimezone() {
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown';
        } catch (e) {
            return 'unknown';
        }
    }

    function getConnectionInfo() {
        try {
            var connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            if (connection) {
                return {
                    effectiveType: connection.effectiveType || '',
                    downlink: connection.downlink || 0,
                    rtt: connection.rtt || 0
                };
            }
        } catch (e) {
            // Ignore errors
        }
        return {};
    }

    // Detect suspicious behavioral patterns
    function detectSuspiciousPatterns() {
        var suspiciousScore = 0;
        
        // Check for perfect timing patterns (bot-like)
        if (behaviorData.clickPattern.length > 2) {
            var timings = [];
            for (var i = 1; i < behaviorData.clickPattern.length; i++) {
                if (behaviorData.clickPattern[i].timeSinceLastClick) {
                    timings.push(behaviorData.clickPattern[i].timeSinceLastClick);
                }
            }
            
            // Check for suspiciously consistent timing
            if (timings.length > 1) {
                var avgTiming = timings.reduce(function(a, b) { return a + b; }) / timings.length;
                var variance = timings.reduce(function(acc, timing) {
                    return acc + Math.pow(timing - avgTiming, 2);
                }, 0) / timings.length;
                
                if (variance < 100) { // Very low variance suggests bot
                    suspiciousScore += 30;
                }
            }
        }
        
        // Check for linear mouse movements (bot-like)
        if (behaviorData.mousePattern.length > 3) {
            var linearMovements = 0;
            for (var j = 2; j < behaviorData.mousePattern.length; j++) {
                var p1 = behaviorData.mousePattern[j-2];
                var p2 = behaviorData.mousePattern[j-1];
                var p3 = behaviorData.mousePattern[j];
                
                // Calculate if movement is too linear
                var slope1 = (p2.y - p1.y) / (p2.x - p1.x);
                var slope2 = (p3.y - p2.y) / (p3.x - p2.x);
                
                if (Math.abs(slope1 - slope2) < 0.1) {
                    linearMovements++;
                }
            }
            
            if (linearMovements / behaviorData.mousePattern.length > 0.8) {
                suspiciousScore += 25;
            }
        }
        
        // Check for no human-like interactions
        if (behaviorData.timeOnPage > 30 && 
            behaviorData.mouseMovements === 0 && 
            behaviorData.keystrokes === 0 && 
            behaviorData.scrollEvents === 0) {
            suspiciousScore += 40;
        }
        
        // Store suspicious score for reporting
        behaviorData.suspiciousScore = suspiciousScore;
    }

    // Calculate comprehensive behavior score
    function calculateBehaviorScore() {
        var score = 0;
        
        // Mouse movement score
        if (behaviorData.mouseMovements > 20) score += 25;
        else if (behaviorData.mouseMovements > 10) score += 15;
        else if (behaviorData.mouseMovements > 5) score += 10;
        
        // Interaction score
        if (behaviorData.clickEvents > 5) score += 20;
        else if (behaviorData.clickEvents > 2) score += 15;
        else if (behaviorData.clickEvents > 0) score += 5;
        
        // Keystroke score
        if (behaviorData.keystrokes > 10) score += 20;
        else if (behaviorData.keystrokes > 5) score += 15;
        else if (behaviorData.keystrokes > 0) score += 5;
        
        // Scroll score
        if (behaviorData.scrollEvents > 10) score += 15;
        else if (behaviorData.scrollEvents > 5) score += 10;
        else if (behaviorData.scrollEvents > 0) score += 5;
        
        // Time on page score
        if (behaviorData.timeOnPage > 60) score += 15;
        else if (behaviorData.timeOnPage > 30) score += 10;
        else if (behaviorData.timeOnPage > 10) score += 5;
        
        // Focus events score
        if (behaviorData.focusEvents > 0) score += 5;
        
        // Subtract suspicious score
        if (behaviorData.suspiciousScore) {
            score -= behaviorData.suspiciousScore;
        }
        
        return Math.max(0, Math.min(score, 100));
    }

    // Track event with enhanced security
    function trackEvent(eventType, status) {
        if (!config.trackingEnabled || !config.ajaxUrl || !canMakeRequest()) {
            return;
        }

        var data = {
            action: 'gotham_track_event',
            nonce: config.nonce,
            event_type: eventType,
            status: status,
            url: window.location.href,
            referer: document.referrer,
            screen_resolution: behaviorData.fingerprint.screenResolution,
            timezone: behaviorData.fingerprint.timezone,
            language: behaviorData.fingerprint.language,
            plugins_count: behaviorData.fingerprint.pluginsCount,
            canvas_fingerprint: behaviorData.fingerprint.canvasFingerprint,
            webgl_vendor: behaviorData.fingerprint.webglVendor,
            webgl_renderer: behaviorData.fingerprint.webglRenderer,
            webdriver_detected: behaviorData.fingerprint.webdriver ? 'true' : 'false',
            mouse_movements: behaviorData.mouseMovements,
            keystrokes: behaviorData.keystrokes,
            scroll_events: behaviorData.scrollEvents,
            click_events: behaviorData.clickEvents,
            focus_events: behaviorData.focusEvents,
            time_on_page: behaviorData.timeOnPage,
            behavior_score: calculateBehaviorScore(),
            session_duration: Math.floor((Date.now() - behaviorData.sessionStart) / 1000)
        };

        $.ajax({
            url: config.ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                eventCount++;
                lastRequestTime = Date.now();
                
                if (config.debugMode) {
                    console.log('Gotham Analytics: Event tracked successfully', response);
                }
            },
            error: function(xhr, status, error) {
                if (config.debugMode) {
                    console.error('Gotham Analytics: Tracking failed', error);
                }
            }
        });
    }

    // Public API
    window.gothamAnalytics = window.gothamAnalytics || {};
    window.gothamAnalytics.trackEvent = trackEvent;
    window.gothamAnalytics.getBehaviorData = function() {
        return behaviorData;
    };
    window.gothamAnalytics.getBehaviorScore = calculateBehaviorScore;

    // Initialize when document is ready
    $(document).ready(function() {
        initTracking();
        
        // Track initial page load
        trackEvent('page_load', 'tracking');
    });

    // Track page unload
    $(window).on('beforeunload', function() {
        trackEvent('page_unload', 'tracking');
    });

})(jQuery);
