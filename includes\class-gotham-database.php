<?php
/**
 * Gotham Block Database Class
 * Handles all database operations with proper security
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Database {
    
    private $table_stats;
    private $table_bot_stats;
    
    public function __construct() {
        global $wpdb;
        $this->table_stats = $wpdb->prefix . 'gotham_adblock_stats';
        $this->table_bot_stats = $wpdb->prefix . 'gotham_bot_stats';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Main analytics table
        $sql_stats = "CREATE TABLE IF NOT EXISTS {$this->table_stats} (
            id int(11) NOT NULL AUTO_INCREMENT,
            ip varchar(45) NOT NULL,
            user_agent text,
            url varchar(500),
            referer varchar(500),
            event_type varchar(50),
            status varchar(20) DEFAULT 'pending',
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            screen_resolution varchar(20),
            timezone varchar(50),
            language varchar(10),
            plugins_count int(11) DEFAULT 0,
            canvas_fingerprint varchar(100),
            webgl_vendor varchar(100),
            webgl_renderer varchar(100),
            webdriver_detected varchar(10) DEFAULT 'false',
            mouse_movements int(11) DEFAULT 0,
            keystrokes int(11) DEFAULT 0,
            scroll_events int(11) DEFAULT 0,
            click_events int(11) DEFAULT 0,
            focus_events int(11) DEFAULT 0,
            time_on_page int(11) DEFAULT 0,
            behavior_score int(11) DEFAULT 0,
            session_duration int(11) DEFAULT 0,
            is_bot tinyint(1) DEFAULT 0,
            bot_confidence decimal(3,2) DEFAULT 0.00,
            PRIMARY KEY (id),
            INDEX idx_ip (ip),
            INDEX idx_timestamp (timestamp),
            INDEX idx_event_type (event_type),
            INDEX idx_status (status),
            INDEX idx_is_bot (is_bot)
        ) $charset_collate;";
        
        // Bot detection table
        $sql_bot_stats = "CREATE TABLE IF NOT EXISTS {$this->table_bot_stats} (
            id int(11) NOT NULL AUTO_INCREMENT,
            ip varchar(45) NOT NULL,
            user_agent text,
            detection_method varchar(50),
            confidence_score decimal(3,2) DEFAULT 0.00,
            is_bot tinyint(1) DEFAULT 0,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_ip (ip),
            INDEX idx_timestamp (timestamp),
            INDEX idx_is_bot (is_bot)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        $result1 = dbDelta($sql_stats);
        $result2 = dbDelta($sql_bot_stats);
        
        // Log any errors
        if ($wpdb->last_error) {
            error_log('Gotham Block DB Error: ' . $wpdb->last_error);
        }
        
        return !empty($result1) && !empty($result2);
    }
    
    /**
     * Insert analytics data with proper sanitization
     */
    public function insert_analytics_data($data) {
        global $wpdb;
        
        $security = new Gotham_Security();
        
        // Sanitize all input data
        $sanitized_data = array(
            'ip' => $security->sanitize_ip($data['ip'] ?? ''),
            'user_agent' => $security->sanitize_user_agent($data['user_agent'] ?? ''),
            'url' => $security->sanitize_url($data['url'] ?? ''),
            'referer' => $security->sanitize_url($data['referer'] ?? ''),
            'event_type' => $security->validate_event_type($data['event_type'] ?? ''),
            'status' => $security->validate_status($data['status'] ?? 'pending'),
            'screen_resolution' => $security->sanitize_screen_resolution($data['screen_resolution'] ?? ''),
            'timezone' => $security->sanitize_timezone($data['timezone'] ?? ''),
            'language' => sanitize_text_field($data['language'] ?? ''),
            'plugins_count' => max(0, intval($data['plugins_count'] ?? 0)),
            'canvas_fingerprint' => sanitize_text_field($data['canvas_fingerprint'] ?? ''),
            'webgl_vendor' => sanitize_text_field($data['webgl_vendor'] ?? ''),
            'webgl_renderer' => sanitize_text_field($data['webgl_renderer'] ?? ''),
            'webdriver_detected' => sanitize_text_field($data['webdriver_detected'] ?? 'false'),
            'mouse_movements' => max(0, intval($data['mouse_movements'] ?? 0)),
            'keystrokes' => max(0, intval($data['keystrokes'] ?? 0)),
            'scroll_events' => max(0, intval($data['scroll_events'] ?? 0)),
            'click_events' => max(0, intval($data['click_events'] ?? 0)),
            'focus_events' => max(0, intval($data['focus_events'] ?? 0)),
            'time_on_page' => max(0, intval($data['time_on_page'] ?? 0)),
            'behavior_score' => max(0, intval($data['behavior_score'] ?? 0)),
            'session_duration' => max(0, intval($data['session_duration'] ?? 0)),
            'is_bot' => intval($data['is_bot'] ?? 0),
            'bot_confidence' => max(0, min(1, floatval($data['bot_confidence'] ?? 0)))
        );
        
        $result = $wpdb->insert(
            $this->table_stats,
            $sanitized_data,
            array(
                '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d',
                '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%d', '%d', '%d',
                '%d', '%d', '%d', '%f'
            )
        );
        
        if ($result === false) {
            error_log('Gotham Block: Failed to insert analytics data - ' . $wpdb->last_error);
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Insert bot detection data
     */
    public function insert_bot_data($data) {
        global $wpdb;
        
        $security = new Gotham_Security();
        
        $sanitized_data = array(
            'ip' => $security->sanitize_ip($data['ip'] ?? ''),
            'user_agent' => $security->sanitize_user_agent($data['user_agent'] ?? ''),
            'detection_method' => sanitize_text_field($data['detection_method'] ?? ''),
            'confidence_score' => max(0, min(1, floatval($data['confidence_score'] ?? 0))),
            'is_bot' => intval($data['is_bot'] ?? 0)
        );
        
        $result = $wpdb->insert(
            $this->table_bot_stats,
            $sanitized_data,
            array('%s', '%s', '%s', '%f', '%d')
        );
        
        if ($result === false) {
            error_log('Gotham Block: Failed to insert bot data - ' . $wpdb->last_error);
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Get analytics data with pagination and proper escaping
     */
    public function get_analytics_data($limit = 100, $offset = 0, $filters = array()) {
        global $wpdb;
        
        $where_clauses = array('1=1');
        $where_values = array();
        
        // Add filters with proper sanitization
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = sanitize_text_field($filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = sanitize_text_field($filters['end_date']);
        }
        
        if (!empty($filters['event_type'])) {
            $where_clauses[] = 'event_type = %s';
            $where_values[] = sanitize_text_field($filters['event_type']);
        }
        
        if (isset($filters['is_bot'])) {
            $where_clauses[] = 'is_bot = %d';
            $where_values[] = intval($filters['is_bot']);
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        
        // Build the query
        $sql = "SELECT * FROM {$this->table_stats} WHERE {$where_sql} ORDER BY timestamp DESC LIMIT %d OFFSET %d";
        $where_values[] = intval($limit);
        $where_values[] = intval($offset);
        
        $prepared_sql = $wpdb->prepare($sql, $where_values);
        
        return $wpdb->get_results($prepared_sql, ARRAY_A);
    }
    
    /**
     * Get analytics count
     */
    public function get_analytics_count($filters = array()) {
        global $wpdb;
        
        $where_clauses = array('1=1');
        $where_values = array();
        
        // Add filters
        if (!empty($filters['start_date'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = sanitize_text_field($filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = sanitize_text_field($filters['end_date']);
        }
        
        if (!empty($filters['event_type'])) {
            $where_clauses[] = 'event_type = %s';
            $where_values[] = sanitize_text_field($filters['event_type']);
        }
        
        if (isset($filters['is_bot'])) {
            $where_clauses[] = 'is_bot = %d';
            $where_values[] = intval($filters['is_bot']);
        }
        
        $where_sql = implode(' AND ', $where_clauses);
        $sql = "SELECT COUNT(*) FROM {$this->table_stats} WHERE {$where_sql}";
        
        if (!empty($where_values)) {
            $prepared_sql = $wpdb->prepare($sql, $where_values);
        } else {
            $prepared_sql = $sql;
        }
        
        return intval($wpdb->get_var($prepared_sql));
    }
    
    /**
     * Clean old data (data retention)
     */
    public function cleanup_old_data($days = 90) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $sql1 = $wpdb->prepare("DELETE FROM {$this->table_stats} WHERE timestamp < %s", $cutoff_date);
        $sql2 = $wpdb->prepare("DELETE FROM {$this->table_bot_stats} WHERE timestamp < %s", $cutoff_date);
        
        $result1 = $wpdb->query($sql1);
        $result2 = $wpdb->query($sql2);
        
        return array(
            'analytics_deleted' => $result1,
            'bot_stats_deleted' => $result2
        );
    }
    
    /**
     * Reset analytics data with proper security
     */
    public function reset_analytics_data() {
        global $wpdb;
        
        // Only allow if user has proper permissions
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        $result1 = $wpdb->query("TRUNCATE TABLE {$this->table_stats}");
        $result2 = $wpdb->query("TRUNCATE TABLE {$this->table_bot_stats}");
        
        return $result1 !== false && $result2 !== false;
    }
    
    /**
     * Get table names
     */
    public function get_table_stats() {
        return $this->table_stats;
    }
    
    public function get_table_bot_stats() {
        return $this->table_bot_stats;
    }
    
    /**
     * Check if tables exist
     */
    public function tables_exist() {
        global $wpdb;
        
        $stats_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_stats}'") === $this->table_stats;
        $bot_stats_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_bot_stats}'") === $this->table_bot_stats;
        
        return $stats_exists && $bot_stats_exists;
    }
    
    /**
     * Migrate database if needed
     */
    public function migrate_if_needed() {
        $current_version = get_option('gotham_block_db_version', '1.0.0');
        
        if (version_compare($current_version, GOTHAM_BLOCK_VERSION, '<')) {
            $this->run_migrations($current_version);
            update_option('gotham_block_db_version', GOTHAM_BLOCK_VERSION);
        }
    }
    
    /**
     * Run database migrations
     */
    private function run_migrations($from_version) {
        // Add migration logic here as needed
        if (version_compare($from_version, '1.5.1', '<')) {
            // Add any new columns or indexes
            $this->add_missing_columns();
        }
    }
    
    /**
     * Add missing columns for backward compatibility
     */
    private function add_missing_columns() {
        global $wpdb;
        
        // Check and add missing columns to stats table
        $columns = $wpdb->get_col("DESCRIBE {$this->table_stats}");
        
        $required_columns = array(
            'is_bot' => 'ALTER TABLE ' . $this->table_stats . ' ADD COLUMN is_bot tinyint(1) DEFAULT 0',
            'bot_confidence' => 'ALTER TABLE ' . $this->table_stats . ' ADD COLUMN bot_confidence decimal(3,2) DEFAULT 0.00'
        );
        
        foreach ($required_columns as $column => $sql) {
            if (!in_array($column, $columns)) {
                $wpdb->query($sql);
            }
        }
        
        // Add indexes if they don't exist
        $this->add_missing_indexes();
    }
    
    /**
     * Add missing database indexes
     */
    private function add_missing_indexes() {
        global $wpdb;
        
        $indexes = array(
            "CREATE INDEX IF NOT EXISTS idx_ip ON {$this->table_stats} (ip)",
            "CREATE INDEX IF NOT EXISTS idx_timestamp ON {$this->table_stats} (timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_event_type ON {$this->table_stats} (event_type)",
            "CREATE INDEX IF NOT EXISTS idx_status ON {$this->table_stats} (status)",
            "CREATE INDEX IF NOT EXISTS idx_is_bot ON {$this->table_stats} (is_bot)"
        );
        
        foreach ($indexes as $index_sql) {
            $wpdb->query($index_sql);
        }
    }
}
