<?php
/**
 * Debug Analytics System
 * This file helps debug the Gotham Block analytics tracking system
 * Access via: /wp-content/plugins/gotham-block-extra-light/debug-analytics.php
 */

// Prevent direct access and ensure WordPress context
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        http_response_code(404);
        die('Not found');
    }
}

// Security check - only allow WordPress administrators
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'), 'Access Denied', array('response' => 403));
}

// Additional security - check nonce if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'gotham_debug_action')) {
        wp_die('Security check failed', 'Security Error', array('response' => 403));
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Gotham Analytics Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .test-button { display: inline-block; margin: 10px 5px; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; cursor: pointer; border: none; }
        .test-button:hover { background: #005a87; color: white; }
        .test-button.danger { background: #dc3545; }
        .test-button.danger:hover { background: #c82333; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .console-log { background: #000; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Gotham Analytics Debug Tool</h1>
    
    <?php
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    
    // Test 1: Database Table Check
    echo '<div class="debug-section">';
    echo '<h2>📊 Database Table Status</h2>';
    
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    if ($table_exists) {
        echo '<div class="success">✅ Table exists: ' . $table . '</div>';
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table");
        echo '<h3>Table Structure:</h3>';
        echo '<table>';
        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td>' . $column->Field . '</td>';
            echo '<td>' . $column->Type . '</td>';
            echo '<td>' . $column->Null . '</td>';
            echo '<td>' . $column->Key . '</td>';
            echo '<td>' . $column->Default . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
        // Check record count
        $total_records = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo '<div class="info">📈 Total records in table: ' . $total_records . '</div>';
        
        if ($total_records > 0) {
            // Show recent records
            $recent_records = $wpdb->get_results("SELECT * FROM $table ORDER BY created_at DESC LIMIT 10");
            echo '<h3>Recent Records (Last 10):</h3>';
            echo '<table>';
            echo '<tr><th>ID</th><th>Event Type</th><th>IP</th><th>Browser</th><th>Status</th><th>Created</th></tr>';
            foreach ($recent_records as $record) {
                echo '<tr>';
                echo '<td>' . $record->id . '</td>';
                echo '<td>' . $record->event_type . '</td>';
                echo '<td>' . substr($record->ip_address, 0, 10) . '...</td>';
                echo '<td>' . $record->browser . '</td>';
                echo '<td>' . $record->status . '</td>';
                echo '<td>' . $record->created_at . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    } else {
        echo '<div class="error">❌ Table does not exist: ' . $table . '</div>';
        echo '<div class="warning">⚠️ Run plugin activation to create the table</div>';
    }
    echo '</div>';
    
    // Test 2: AJAX Endpoints
    echo '<div class="debug-section">';
    echo '<h2>🔌 AJAX Endpoints Test</h2>';
    
    $ajax_url = admin_url('admin-ajax.php');
    echo '<div class="info">AJAX URL: ' . $ajax_url . '</div>';
    
    // Test nonce generation
    $nonce = wp_create_nonce('gotham_adblock_nonce');
    echo '<div class="info">Generated nonce: ' . $nonce . '</div>';
    
    // Check if AJAX actions are registered
    $actions = [
        'gotham_adblock_track_event' => 'Main tracking endpoint',
        'gotham_track_adblock_action' => 'Legacy tracking endpoint'
    ];
    
    foreach ($actions as $action => $description) {
        if (has_action('wp_ajax_' . $action) || has_action('wp_ajax_nopriv_' . $action)) {
            echo '<div class="success">✅ AJAX action registered: ' . $action . ' (' . $description . ')</div>';
        } else {
            echo '<div class="error">❌ AJAX action not registered: ' . $action . '</div>';
        }
    }
    echo '</div>';
    
    // Test 3: JavaScript Files
    echo '<div class="debug-section">';
    echo '<h2>📜 JavaScript Files Test</h2>';
    
    $js_files = [
        'analytics-tracker.js' => 'Main analytics tracker',
        'assets/analytics-admin.js' => 'Admin dashboard script'
    ];
    
    foreach ($js_files as $file => $description) {
        $file_path = plugin_dir_path(__FILE__) . $file;
        $file_url = plugin_dir_url(__FILE__) . $file;
        
        if (file_exists($file_path)) {
            $size = filesize($file_path);
            echo '<div class="success">✅ ' . $description . ' exists (' . number_format($size) . ' bytes)</div>';
            echo '<div class="info">URL: ' . $file_url . '</div>';
        } else {
            echo '<div class="error">❌ ' . $description . ' missing: ' . $file . '</div>';
        }
    }
    echo '</div>';
    
    // Test 4: Manual Test Buttons
    echo '<div class="debug-section">';
    echo '<h2>🧪 Manual Testing</h2>';
    
    echo '<div class="info">Use these buttons to manually test the analytics system:</div>';
    
    echo '<button class="test-button" onclick="testPopupDisplayed()">Test Popup Displayed Event</button>';
    echo '<button class="test-button" onclick="testPopupClosed()">Test Popup Closed Event</button>';
    echo '<button class="test-button" onclick="testAdblockDisabled()">Test Adblock Disabled Event</button>';
    echo '<button class="test-button danger" onclick="clearAnalyticsData()">Clear All Analytics Data</button>';
    
    echo '<h3>Console Output:</h3>';
    echo '<div id="console-output" class="console-log">Ready for testing...\n</div>';
    echo '</div>';
    
    // Test 5: Current Settings
    echo '<div class="debug-section">';
    echo '<h2>⚙️ Current Plugin Settings</h2>';
    
    $settings = [
        'gothamadblock_option_fury' => 'Detection Mode',
        'gothamadblock_option_cookietime' => 'Cookie Time',
        'gothamadblock_option_messageperso_title' => 'Custom Title',
        'gothamadblock_option_messageperso' => 'Custom Message',
        'gothamadblock_option_messageperso_button' => 'Custom Button'
    ];
    
    foreach ($settings as $option => $label) {
        $value = get_option($option);
        echo '<div class="info">' . $label . ': <code>' . esc_html($value ?: 'Not set') . '</code></div>';
    }
    echo '</div>';
    ?>
    
    <script>
    // Add console logging
    function log(message) {
        const output = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        output.innerHTML += '[' + timestamp + '] ' + message + '\n';
        output.scrollTop = output.scrollHeight;
    }
    
    // Test functions
    function testPopupDisplayed() {
        log('Testing popup displayed event...');
        
        if (typeof gotham_send_analytics === 'function') {
            gotham_send_analytics('pop_displayed');
            log('✅ Sent pop_displayed event');
        } else {
            log('❌ gotham_send_analytics function not available');
            // Fallback: manual AJAX call
            testManualAjax('pop_displayed');
        }
    }
    
    function testPopupClosed() {
        log('Testing popup closed event...');
        
        if (typeof gotham_send_analytics === 'function') {
            gotham_send_analytics('popup_closed');
            log('✅ Sent popup_closed event');
        } else {
            log('❌ gotham_send_analytics function not available');
            testManualAjax('popup_closed');
        }
    }
    
    function testAdblockDisabled() {
        log('Testing adblock disabled event...');
        
        if (typeof gotham_send_analytics === 'function') {
            gotham_send_analytics('adblock_disabled');
            log('✅ Sent adblock_disabled event');
        } else {
            log('❌ gotham_send_analytics function not available');
            testManualAjax('adblock_disabled');
        }
    }
    
    function testManualAjax(eventType) {
        log('Attempting manual AJAX call for: ' + eventType);
        
        const data = new FormData();
        data.append('action', 'gotham_adblock_track_event');
        data.append('event_type', eventType);
        data.append('status', 'test');
        data.append('nonce', '<?php echo wp_create_nonce('gotham_adblock_nonce'); ?>');
        data.append('screen_resolution', screen.width + 'x' + screen.height);
        data.append('timezone', Intl.DateTimeFormat().resolvedOptions().timeZone || '');
        data.append('page_url', window.location.href);
        data.append('referrer_url', document.referrer || '');
        
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            body: data
        })
        .then(res => res.json())
        .then(json => {
            if (json.success) {
                log('✅ Manual AJAX success: ' + JSON.stringify(json.data));
            } else {
                log('❌ Manual AJAX failed: ' + JSON.stringify(json.data));
            }
        })
        .catch(e => {
            log('❌ Manual AJAX error: ' + e.message);
        });
    }
    
    function clearAnalyticsData() {
        if (confirm('Are you sure you want to clear ALL analytics data? This cannot be undone!')) {
            log('Clearing analytics data...');
            
            const data = new FormData();
            data.append('action', 'gotham_clear_analytics_data');
            data.append('nonce', '<?php echo wp_create_nonce('gotham_clear_analytics'); ?>');
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: data
            })
            .then(res => res.json())
            .then(json => {
                if (json.success) {
                    log('✅ Analytics data cleared successfully');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    log('❌ Failed to clear analytics data: ' + JSON.stringify(json.data));
                }
            })
            .catch(e => {
                log('❌ Error clearing analytics data: ' + e.message);
            });
        }
    }
    
    // Load analytics tracker if available
    log('Debug tool loaded. Checking for analytics tracker...');
    
    // Try to load the analytics tracker
    const script = document.createElement('script');
    script.src = '<?php echo plugin_dir_url(__FILE__); ?>analytics-tracker.js?v=' + Date.now();
    script.onload = function() {
        log('✅ Analytics tracker loaded successfully');
    };
    script.onerror = function() {
        log('❌ Failed to load analytics tracker');
    };
    document.head.appendChild(script);
    </script>
    
</body>
</html>

<?php
// Add AJAX handler for clearing analytics data
add_action('wp_ajax_gotham_clear_analytics_data', function() {
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'gotham_clear_analytics')) {
        wp_send_json_error(['message' => 'Invalid nonce']);
        return;
    }
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }
    
    global $wpdb;
    $table = $wpdb->prefix . 'gotham_adblock_stats';
    
    $result = $wpdb->query("TRUNCATE TABLE $table");
    
    if ($result !== false) {
        wp_send_json_success(['message' => 'Analytics data cleared successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to clear analytics data', 'error' => $wpdb->last_error]);
    }
});
?>
