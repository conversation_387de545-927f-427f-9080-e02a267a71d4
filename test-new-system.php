<?php
/**
 * Test script for the new Gotham Block system
 * This file tests the new class-based architecture
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        die('WordPress not found');
    }
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>Gotham Block System Test</h1>';

// Test 1: Check if new classes exist
echo '<h2>1. Class Availability Test</h2>';
$classes = [
    'Gotham_Block_Plugin',
    'Gotham_Security',
    'Gotham_Database',
    'Gotham_Bot_Detector',
    'Gotham_Analytics',
    'Gotham_Popup'
];

foreach ($classes as $class) {
    if (class_exists($class)) {
        echo "<p>✅ {$class} - Available</p>";
    } else {
        echo "<p>❌ {$class} - Missing</p>";
    }
}

// Test 2: Check plugin instance
echo '<h2>2. Plugin Instance Test</h2>';
try {
    $plugin = Gotham_Block_Plugin::get_instance();
    if ($plugin) {
        echo "<p>✅ Plugin instance created successfully</p>";
        
        // Test component access
        $security = $plugin->get_security();
        $database = $plugin->get_database();
        $bot_detector = $plugin->get_bot_detector();
        $analytics = $plugin->get_analytics();
        $popup = $plugin->get_popup();
        
        echo "<p>✅ All components accessible</p>";
    } else {
        echo "<p>❌ Failed to create plugin instance</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error creating plugin instance: " . $e->getMessage() . "</p>";
}

// Test 3: Database functionality
echo '<h2>3. Database Test</h2>';
try {
    $database = new Gotham_Database();
    
    if ($database->tables_exist()) {
        echo "<p>✅ Database tables exist</p>";
    } else {
        echo "<p>⚠️ Database tables missing - creating...</p>";
        if ($database->create_tables()) {
            echo "<p>✅ Database tables created successfully</p>";
        } else {
            echo "<p>❌ Failed to create database tables</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: Security functionality
echo '<h2>4. Security Test</h2>';
try {
    $security = new Gotham_Security();
    
    // Test nonce creation
    $nonce = $security->create_nonce();
    if ($nonce) {
        echo "<p>✅ Nonce creation works</p>";
        
        // Test nonce verification
        if ($security->verify_nonce($nonce)) {
            echo "<p>✅ Nonce verification works</p>";
        } else {
            echo "<p>❌ Nonce verification failed</p>";
        }
    } else {
        echo "<p>❌ Nonce creation failed</p>";
    }
    
    // Test IP sanitization
    $test_ip = '***********';
    $sanitized_ip = $security->sanitize_ip($test_ip);
    if ($sanitized_ip === $test_ip) {
        echo "<p>✅ IP sanitization works</p>";
    } else {
        echo "<p>❌ IP sanitization failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Security error: " . $e->getMessage() . "</p>";
}

// Test 5: Bot detection functionality
echo '<h2>5. Bot Detection Test</h2>';
try {
    $bot_detector = new Gotham_Bot_Detector();
    
    // Test with known bot user agent
    $bot_ua = 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)';
    $bot_result = $bot_detector->detect_bot($bot_ua);
    
    if ($bot_result['is_bot']) {
        echo "<p>✅ Bot detection works (detected Googlebot)</p>";
    } else {
        echo "<p>❌ Bot detection failed (should detect Googlebot)</p>";
    }
    
    // Test with normal browser user agent
    $normal_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    $normal_result = $bot_detector->detect_bot($normal_ua);
    
    if (!$normal_result['is_bot']) {
        echo "<p>✅ Bot detection works (normal browser not detected as bot)</p>";
    } else {
        echo "<p>⚠️ Bot detection may be too sensitive (normal browser detected as bot)</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Bot detection error: " . $e->getMessage() . "</p>";
}

// Test 6: Analytics functionality
echo '<h2>6. Analytics Test</h2>';
try {
    $database = new Gotham_Database();
    $bot_detector = new Gotham_Bot_Detector();
    $analytics = new Gotham_Analytics($database, $bot_detector);
    
    // Test summary data
    $summary = $analytics->get_admin_summary();
    if (is_array($summary)) {
        echo "<p>✅ Analytics summary generation works</p>";
        echo "<p>Today's events: " . $summary['today'] . "</p>";
        echo "<p>Bot detection stats available: " . (isset($summary['bot_stats']) ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p>❌ Analytics summary generation failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Analytics error: " . $e->getMessage() . "</p>";
}

// Test 7: Configuration test
echo '<h2>7. Configuration Test</h2>';
$options = [
    'gothamadblock_option_fury' => get_option('gothamadblock_option_fury'),
    'gothamadblock_option_cookietime' => get_option('gothamadblock_option_cookietime'),
    'gothamadblock_option_popup_delay' => get_option('gothamadblock_option_popup_delay'),
    'gothamadblock_option_powered' => get_option('gothamadblock_option_powered'),
    'gothamadblock_option_premium_tools' => get_option('gothamadblock_option_premium_tools')
];

foreach ($options as $option => $value) {
    if ($value !== false) {
        echo "<p>✅ {$option}: {$value}</p>";
    } else {
        echo "<p>⚠️ {$option}: Not set</p>";
    }
}

// Test 8: File structure test
echo '<h2>8. File Structure Test</h2>';
$required_files = [
    'includes/class-gotham-security.php',
    'includes/class-gotham-database.php',
    'includes/class-gotham-bot-detector.php',
    'includes/class-gotham-analytics.php',
    'includes/class-gotham-popup.php',
    'admin/class-gotham-admin.php',
    'assets/analytics-tracker.js',
    'assets/popup-style.css',
    'assets/popup-script.js',
    'assets/admin-style.css',
    'assets/admin-script.js'
];

$plugin_dir = dirname(__FILE__) . '/';
foreach ($required_files as $file) {
    if (file_exists($plugin_dir . $file)) {
        echo "<p>✅ {$file} - Exists</p>";
    } else {
        echo "<p>❌ {$file} - Missing</p>";
    }
}

echo '<h2>Test Complete</h2>';
echo '<p><strong>Summary:</strong> The new Gotham Block system has been tested. Check the results above for any issues that need to be addressed.</p>';
echo '<p><a href="' . admin_url('options-general.php?page=gotham-block-settings') . '">Go to Settings</a> | ';
echo '<a href="' . admin_url('tools.php?page=gotham-block-analytics') . '">Go to Analytics</a></p>';
?>
