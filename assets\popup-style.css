/**
 * Gotham Block Popup Styles
 * Modern, responsive popup design
 */

/* Overlay */
.gotham-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
    animation: gotham-fade-in 0.3s ease-out;
}

/* Container */
.gotham-popup-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: gotham-slide-up 0.4s ease-out;
}

/* Header */
.gotham-popup-header {
    padding: 20px 20px 0;
    position: relative;
}

.gotham-popup-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.gotham-popup-close:hover {
    background: #f0f0f0;
    color: #333;
}

/* Content */
.gotham-popup-content {
    padding: 20px 30px;
    text-align: center;
}

.gotham-popup-content h3 {
    margin: 0 0 15px;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.gotham-popup-content p {
    margin: 0 0 15px;
    font-size: 16px;
    line-height: 1.5;
    color: #555;
}

.gotham-popup-content strong {
    color: #d63384;
}

/* Actions */
.gotham-popup-actions {
    padding: 0 30px 30px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.gotham-popup-button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.gotham-btn-primary {
    background: #007cba;
    color: white;
}

.gotham-btn-primary:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.gotham-btn-secondary {
    background: #6c757d;
    color: white;
}

.gotham-btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.gotham-btn-tertiary {
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.gotham-btn-tertiary:hover {
    background: #f8f9fa;
    color: #495057;
}

/* Footer */
.gotham-popup-footer {
    padding: 0 30px 20px;
    text-align: center;
    border-top: 1px solid #eee;
    margin-top: 20px;
    padding-top: 15px;
}

.gotham-popup-footer small {
    color: #999;
    font-size: 12px;
}

.gotham-popup-footer a {
    color: #007cba;
    text-decoration: none;
}

.gotham-popup-footer a:hover {
    text-decoration: underline;
}

/* Fury Mode Variations */
.gotham-fury-ssj1 {
    border-top: 4px solid #28a745;
}

.gotham-fury-ssj1 .gotham-popup-content h3 {
    color: #28a745;
}

.gotham-fury-ssj2 {
    border-top: 4px solid #ffc107;
}

.gotham-fury-ssj2 .gotham-popup-content h3 {
    color: #e0a800;
}

.gotham-fury-ssj3 {
    border-top: 4px solid #dc3545;
}

.gotham-fury-ssj3 .gotham-popup-content h3 {
    color: #dc3545;
}

.gotham-fury-ssj3 .gotham-popup-content {
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .gotham-popup-container {
        width: 95%;
        margin: 20px;
    }
    
    .gotham-popup-content {
        padding: 15px 20px;
    }
    
    .gotham-popup-content h3 {
        font-size: 20px;
    }
    
    .gotham-popup-content p {
        font-size: 14px;
    }
    
    .gotham-popup-actions {
        padding: 0 20px 20px;
    }
    
    .gotham-popup-button {
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .gotham-popup-container {
        width: 100%;
        margin: 10px;
        border-radius: 8px;
    }
    
    .gotham-popup-content h3 {
        font-size: 18px;
    }
    
    .gotham-popup-actions {
        gap: 8px;
    }
}

/* Animations */
@keyframes gotham-fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes gotham-slide-up {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Loading state */
.gotham-popup-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
    position: relative;
}

.gotham-popup-button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: gotham-spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes gotham-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Accessibility */
.gotham-popup-overlay:focus-within .gotham-popup-container {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.gotham-popup-button:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.gotham-popup-close:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gotham-popup-container {
        border: 2px solid #000;
    }
    
    .gotham-popup-button {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gotham-popup-overlay,
    .gotham-popup-container,
    .gotham-popup-button,
    .gotham-popup-close {
        animation: none;
        transition: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .gotham-popup-container {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .gotham-popup-content h3 {
        color: #f7fafc;
    }
    
    .gotham-popup-content p {
        color: #cbd5e0;
    }
    
    .gotham-popup-close {
        color: #a0aec0;
    }
    
    .gotham-popup-close:hover {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .gotham-popup-footer {
        border-top-color: #4a5568;
    }
    
    .gotham-btn-tertiary {
        border-color: #4a5568;
        color: #a0aec0;
    }
    
    .gotham-btn-tertiary:hover {
        background: #4a5568;
        color: #e2e8f0;
    }
}
