/**
 * Gotham Block Admin Script
 * Handles admin interface interactions and analytics dashboard
 */

(function($) {
    'use strict';

    // Configuration
    var config = {
        ajaxUrl: window.gothamAdmin ? window.gothamAdmin.ajaxUrl : '',
        nonce: window.gothamAdmin ? window.gothamAdmin.nonce : '',
        strings: window.gothamAdmin ? window.gothamAdmin.strings : {}
    };

    // Charts storage
    var charts = {};

    // Initialize when document is ready
    $(document).ready(function() {
        initializeAdmin();
    });

    /**
     * Initialize admin functionality
     */
    function initializeAdmin() {
        bindEventHandlers();
        initializeCharts();
        loadAnalyticsData();
    }

    /**
     * Bind event handlers
     */
    function bindEventHandlers() {
        // Reset data button
        $('#gotham-reset-data').on('click', function(e) {
            e.preventDefault();
            handleResetData();
        });

        // Export data button
        $('#gotham-export-data').on('click', function(e) {
            e.preventDefault();
            handleExportData();
        });

        // Run bot detection button
        $('#gotham-run-bot-detection').on('click', function(e) {
            e.preventDefault();
            handleRunBotDetection();
        });

        // Analytics filters form
        $('#gotham-analytics-filters').on('submit', function(e) {
            e.preventDefault();
            loadAnalyticsData();
        });

        // Auto-refresh analytics data every 5 minutes
        setInterval(function() {
            loadAnalyticsData(true);
        }, 300000);
    }

    /**
     * Handle reset data action
     */
    function handleResetData() {
        if (!confirm(config.strings.confirmReset || 'Are you sure you want to reset all analytics data?')) {
            return;
        }

        var button = $('#gotham-reset-data');
        var originalText = button.text();
        
        button.text(config.strings.processing || 'Processing...').prop('disabled', true);

        $.ajax({
            url: config.ajaxUrl,
            type: 'POST',
            data: {
                action: 'gotham_reset_data',
                nonce: config.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('success', 'Analytics data reset successfully');
                    loadAnalyticsData();
                } else {
                    showNotice('error', response.data || 'Failed to reset data');
                }
            },
            error: function() {
                showNotice('error', config.strings.error || 'An error occurred');
            },
            complete: function() {
                button.text(originalText).prop('disabled', false);
            }
        });
    }

    /**
     * Handle export data action
     */
    function handleExportData() {
        if (!confirm(config.strings.confirmExport || 'Export analytics data?')) {
            return;
        }

        var button = $('#gotham-export-data');
        var originalText = button.text();
        
        button.text(config.strings.processing || 'Processing...').prop('disabled', true);

        var filters = getAnalyticsFilters();
        
        // Create form for file download
        var form = $('<form>', {
            method: 'POST',
            action: config.ajaxUrl
        });

        form.append($('<input>', {
            type: 'hidden',
            name: 'action',
            value: 'gotham_export_data'
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'nonce',
            value: config.nonce
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'start_date',
            value: filters.start_date
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'end_date',
            value: filters.end_date
        }));

        form.appendTo('body').submit().remove();

        setTimeout(function() {
            button.text(originalText).prop('disabled', false);
        }, 2000);
    }

    /**
     * Handle run bot detection action
     */
    function handleRunBotDetection() {
        var button = $('#gotham-run-bot-detection');
        var originalText = button.text();
        
        button.text(config.strings.processing || 'Processing...').prop('disabled', true);

        $.ajax({
            url: config.ajaxUrl,
            type: 'POST',
            data: {
                action: 'gotham_run_bot_detection',
                nonce: config.nonce
            },
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    showNotice('success', 'Bot detection completed. Processed: ' + data.processed + ', Updated: ' + data.updated);
                    loadAnalyticsData();
                } else {
                    showNotice('error', response.data || 'Failed to run bot detection');
                }
            },
            error: function() {
                showNotice('error', config.strings.error || 'An error occurred');
            },
            complete: function() {
                button.text(originalText).prop('disabled', false);
            }
        });
    }

    /**
     * Load analytics data
     */
    function loadAnalyticsData(silent) {
        if (!silent) {
            $('#gotham-analytics-data').addClass('gotham-loading');
        }

        var filters = getAnalyticsFilters();

        $.ajax({
            url: config.ajaxUrl,
            type: 'POST',
            data: {
                action: 'gotham_get_analytics_data',
                nonce: config.nonce,
                start_date: filters.start_date,
                end_date: filters.end_date
            },
            success: function(response) {
                if (response.success) {
                    updateDashboard(response.data);
                } else {
                    if (!silent) {
                        showNotice('error', 'Failed to load analytics data');
                    }
                }
            },
            error: function() {
                if (!silent) {
                    showNotice('error', config.strings.error || 'An error occurred');
                }
            },
            complete: function() {
                $('#gotham-analytics-data').removeClass('gotham-loading');
            }
        });
    }

    /**
     * Get analytics filters
     */
    function getAnalyticsFilters() {
        return {
            start_date: $('#start_date').val(),
            end_date: $('#end_date').val()
        };
    }

    /**
     * Update dashboard with new data
     */
    function updateDashboard(data) {
        // Update stat boxes
        updateStatBoxes(data);
        
        // Update charts
        updateCharts(data);
    }

    /**
     * Update stat boxes
     */
    function updateStatBoxes(data) {
        // Update existing stat boxes if they exist
        $('.gotham-stat-box').each(function() {
            var box = $(this);
            var type = box.data('type');
            
            if (type && data[type] !== undefined) {
                box.find('.gotham-stat-number').text(data[type]);
            }
        });
    }

    /**
     * Initialize charts
     */
    function initializeCharts() {
        // Daily chart
        var dailyCtx = document.getElementById('gotham-daily-chart');
        if (dailyCtx) {
            charts.daily = new Chart(dailyCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Total Events',
                        data: [],
                        borderColor: '#2271b1',
                        backgroundColor: 'rgba(34, 113, 177, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Bot Events',
                        data: [],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Daily Analytics'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Events chart
        var eventsCtx = document.getElementById('gotham-events-chart');
        if (eventsCtx) {
            charts.events = new Chart(eventsCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#2271b1',
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#6c757d'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Event Types'
                        }
                    }
                }
            });
        }
    }

    /**
     * Update charts with new data
     */
    function updateCharts(data) {
        // Update daily chart
        if (charts.daily && data.daily_stats) {
            var labels = [];
            var totalData = [];
            var botData = [];

            data.daily_stats.forEach(function(day) {
                labels.push(day.date);
                totalData.push(day.total);
                botData.push(day.bots);
            });

            charts.daily.data.labels = labels;
            charts.daily.data.datasets[0].data = totalData;
            charts.daily.data.datasets[1].data = botData;
            charts.daily.update();
        }

        // Update events chart
        if (charts.events && data.event_breakdown) {
            var labels = [];
            var eventData = [];

            data.event_breakdown.forEach(function(event) {
                labels.push(event.event_type);
                eventData.push(event.count);
            });

            charts.events.data.labels = labels;
            charts.events.data.datasets[0].data = eventData;
            charts.events.update();
        }
    }

    /**
     * Show admin notice
     */
    function showNotice(type, message) {
        var notice = $('<div>', {
            class: 'notice notice-' + type + ' is-dismissible',
            html: '<p>' + message + '</p>'
        });

        $('.wrap h1').after(notice);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            notice.fadeOut(function() {
                notice.remove();
            });
        }, 5000);
    }

    /**
     * Format numbers for display
     */
    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * Format percentage for display
     */
    function formatPercentage(num) {
        return parseFloat(num).toFixed(1) + '%';
    }

    // Make functions available globally for debugging
    window.gothamAdmin = {
        loadAnalyticsData: loadAnalyticsData,
        updateDashboard: updateDashboard,
        charts: charts
    };

})(jQuery);
