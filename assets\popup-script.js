/**
 * Gotham Block Popup Script
 * Handles popup display, interactions, and adblock detection
 */

(function($) {
    'use strict';

    // Configuration
    var config = {
        ajaxUrl: window.gothamPopup ? window.gothamPopup.ajaxUrl : '',
        nonce: window.gothamPopup ? window.gothamPopup.nonce : '',
        delay: window.gothamPopup ? parseInt(window.gothamPopup.delay) : 0,
        cookieTime: window.gothamPopup ? parseInt(window.gothamPopup.cookieTime) : 2592000,
        furyMode: window.gothamPopup ? window.gothamPopup.furyMode : 'ssj1',
        debugMode: window.gothamPopup ? window.gothamPopup.debugMode : false
    };

    // State management
    var state = {
        popupShown: false,
        adblockDetected: false,
        userInteracted: false
    };

    // Initialize when document is ready
    $(document).ready(function() {
        initializePopup();
    });

    /**
     * Initialize popup functionality
     */
    function initializePopup() {
        if (config.debugMode) {
            console.log('Gotham Popup: Initializing...');
        }

        // Check if popup should be shown
        if (shouldShowPopup()) {
            detectAdblock();
        }

        // Bind event handlers
        bindEventHandlers();
    }

    /**
     * Check if popup should be displayed
     */
    function shouldShowPopup() {
        // Check if user has already seen popup
        if (getCookie('gotham_adblock_seen')) {
            if (config.debugMode) {
                console.log('Gotham Popup: User has already seen popup');
            }
            return false;
        }

        // Check fury mode
        if (config.furyMode === 'paused') {
            if (config.debugMode) {
                console.log('Gotham Popup: Plugin is paused');
            }
            return false;
        }

        return true;
    }

    /**
     * Detect adblock using multiple methods
     */
    function detectAdblock() {
        var detectionMethods = [
            detectAdblockByElement,
            detectAdblockByRequest,
            detectAdblockByBait
        ];

        var detectionPromises = detectionMethods.map(function(method) {
            return new Promise(function(resolve) {
                try {
                    method(resolve);
                } catch (e) {
                    resolve(false);
                }
            });
        });

        Promise.all(detectionPromises).then(function(results) {
            var adblockDetected = results.some(function(result) {
                return result === true;
            });

            if (adblockDetected) {
                state.adblockDetected = true;
                
                if (config.delay > 0) {
                    setTimeout(function() {
                        showPopup();
                    }, config.delay * 1000);
                } else {
                    showPopup();
                }

                // Track popup display
                trackEvent('pop_displayed', 'pending');
            } else {
                if (config.debugMode) {
                    console.log('Gotham Popup: No adblock detected');
                }
            }
        });
    }

    /**
     * Detect adblock by creating test element
     */
    function detectAdblockByElement(callback) {
        var testAd = document.createElement('div');
        testAd.innerHTML = '&nbsp;';
        testAd.className = 'adsbox publicite ads ad-banner advertisement';
        testAd.style.cssText = 'position: absolute !important; left: -10000px !important; top: -10000px !important; width: 1px !important; height: 1px !important;';
        
        document.body.appendChild(testAd);
        
        setTimeout(function() {
            var isBlocked = testAd.offsetHeight === 0 || 
                           testAd.offsetWidth === 0 || 
                           testAd.style.display === 'none' ||
                           testAd.style.visibility === 'hidden';
            
            document.body.removeChild(testAd);
            callback(isBlocked);
        }, 100);
    }

    /**
     * Detect adblock by making request to common ad URL
     */
    function detectAdblockByRequest(callback) {
        var img = new Image();
        var timeout = setTimeout(function() {
            callback(true); // Assume blocked if timeout
        }, 3000);

        img.onload = function() {
            clearTimeout(timeout);
            callback(false); // Not blocked
        };

        img.onerror = function() {
            clearTimeout(timeout);
            callback(true); // Blocked
        };

        // Use a common ad-related URL that adblockers typically block
        img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    }

    /**
     * Detect adblock using bait element
     */
    function detectAdblockByBait(callback) {
        var bait = document.createElement('div');
        bait.setAttribute('class', 'pub_300x250 pub_300x250m pub_728x90 text-ad textAd text_ad text_ads text-ads text-ad-links');
        bait.setAttribute('style', 'width: 1px !important; height: 1px !important; position: absolute !important; left: -10000px !important; top: -10000px !important;');
        
        document.body.appendChild(bait);
        
        setTimeout(function() {
            var isBlocked = bait.offsetParent === null || 
                           bait.offsetHeight === 0 || 
                           bait.offsetWidth === 0 ||
                           window.getComputedStyle(bait).getPropertyValue('display') === 'none';
            
            document.body.removeChild(bait);
            callback(isBlocked);
        }, 100);
    }

    /**
     * Show the popup
     */
    function showPopup() {
        if (state.popupShown) {
            return;
        }

        var popup = $('#gotham-adblock-popup');
        if (popup.length === 0) {
            if (config.debugMode) {
                console.error('Gotham Popup: Popup element not found');
            }
            return;
        }

        state.popupShown = true;
        popup.fadeIn(300);
        
        // Prevent body scrolling
        $('body').addClass('gotham-popup-open');
        
        // Focus management for accessibility
        popup.find('.gotham-popup-close').focus();

        if (config.debugMode) {
            console.log('Gotham Popup: Popup displayed');
        }
    }

    /**
     * Hide the popup
     */
    function hidePopup() {
        var popup = $('#gotham-adblock-popup');
        popup.fadeOut(300);
        
        // Restore body scrolling
        $('body').removeClass('gotham-popup-open');
        
        state.popupShown = false;

        if (config.debugMode) {
            console.log('Gotham Popup: Popup hidden');
        }
    }

    /**
     * Bind event handlers
     */
    function bindEventHandlers() {
        // Close button
        $(document).on('click', '.gotham-popup-close', function(e) {
            e.preventDefault();
            handlePopupClose('close_button');
        });

        // Overlay click
        $(document).on('click', '.gotham-popup-overlay', function(e) {
            if (e.target === this) {
                handlePopupClose('overlay_click');
            }
        });

        // Escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && state.popupShown) { // Escape key
                handlePopupClose('escape_key');
            }
        });

        // Action buttons
        $(document).on('click', '.gotham-popup-button', function(e) {
            e.preventDefault();
            var action = $(this).data('action');
            handlePopupAction(action, this);
        });
    }

    /**
     * Handle popup close
     */
    function handlePopupClose(method) {
        if (!state.userInteracted) {
            trackEvent('popup_closed', 'declined');
            state.userInteracted = true;
        }

        hidePopup();
        setCookie('gotham_adblock_seen', '1', config.cookieTime);

        if (config.debugMode) {
            console.log('Gotham Popup: Closed via', method);
        }
    }

    /**
     * Handle popup action button clicks
     */
    function handlePopupAction(action, button) {
        if (state.userInteracted) {
            return;
        }

        state.userInteracted = true;
        
        // Add loading state
        $(button).addClass('loading').prop('disabled', true);

        // Track the action
        trackPopupAction(action).then(function() {
            handleActionResponse(action);
        }).catch(function(error) {
            if (config.debugMode) {
                console.error('Gotham Popup: Action tracking failed', error);
            }
            handleActionResponse(action);
        }).finally(function() {
            $(button).removeClass('loading').prop('disabled', false);
        });
    }

    /**
     * Handle action response
     */
    function handleActionResponse(action) {
        switch (action) {
            case 'disable_adblock':
            case 'whitelist':
            case 'disable_required':
                // Set flag to check for adblock disabled after reload
                sessionStorage.setItem('gotham_check_adblock_disabled', 'true');
                window.location.reload();
                break;
                
            case 'donate':
                // Could open donation page
                hidePopup();
                setCookie('gotham_adblock_seen', '1', config.cookieTime);
                break;
                
            case 'continue':
            case 'decline':
            default:
                hidePopup();
                setCookie('gotham_adblock_seen', '1', config.cookieTime);
                break;
        }
    }

    /**
     * Track popup action
     */
    function trackPopupAction(action) {
        return new Promise(function(resolve, reject) {
            if (!config.ajaxUrl || !config.nonce) {
                reject('Missing AJAX configuration');
                return;
            }

            $.ajax({
                url: config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gotham_popup_action',
                    nonce: config.nonce,
                    popup_action: action,
                    url: window.location.href,
                    referer: document.referrer
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    reject(error);
                }
            });
        });
    }

    /**
     * Track event
     */
    function trackEvent(eventType, status) {
        if (window.gothamAnalytics && typeof window.gothamAnalytics.trackEvent === 'function') {
            window.gothamAnalytics.trackEvent(eventType, status);
        }
    }

    /**
     * Cookie utilities
     */
    function setCookie(name, value, seconds) {
        var expires = new Date();
        expires.setTime(expires.getTime() + (seconds * 1000));
        document.cookie = name + '=' + value + ';expires=' + expires.toUTCString() + ';path=/';
    }

    function getCookie(name) {
        var nameEQ = name + '=';
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // Check for adblock disabled after page reload
    if (sessionStorage.getItem('gotham_check_adblock_disabled') === 'true') {
        sessionStorage.removeItem('gotham_check_adblock_disabled');
        
        // Re-run adblock detection to verify it's disabled
        setTimeout(function() {
            detectAdblockByElement(function(isBlocked) {
                if (!isBlocked) {
                    // Adblock is now disabled - track conversion
                    trackEvent('adblock_disabled', 'verified');
                    
                    if (config.debugMode) {
                        console.log('Gotham Popup: Adblock disabled - conversion tracked');
                    }
                }
            });
        }, 1000);
    }

    // Global function for manual popup display (for testing)
    window.gothamShowPopup = function() {
        if (shouldShowPopup()) {
            showPopup();
        }
    };

    // Add CSS for popup open state
    $('<style>')
        .prop('type', 'text/css')
        .html('.gotham-popup-open { overflow: hidden; }')
        .appendTo('head');

})(jQuery);
