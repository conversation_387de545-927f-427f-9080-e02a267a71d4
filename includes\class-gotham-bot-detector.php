<?php
/**
 * Gotham Block Bot Detector Class
 * Consolidated and improved bot detection functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Gotham_Bot_Detector {
    
    private $bot_patterns;
    private $browser_patterns;
    
    public function __construct() {
        $this->init_patterns();
    }
    
    /**
     * Initialize detection patterns
     */
    private function init_patterns() {
        $this->bot_patterns = array(
            // Search engine bots
            'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot',
            'facebookexternalhit', 'twitterbot', 'linkedinbot', 'whatsapp', 'telegrambot',
            
            // SEO and monitoring bots
            'ahrefsbot', 'semrushbot', 'mj12bot', 'dotbot', 'rogerbot', 'exabot',
            'sogou', 'ia_archiver', 'wayback', 'archive.org_bot',
            
            // Security and scanning bots
            'nmap', 'masscan', 'zmap', 'nikto', 'sqlmap', 'w3af', 'skipfish',
            'openvas', 'nessus', 'acunetix', 'burpsuite', 'owasp',
            
            // Headless browsers and automation
            'headlesschrome', 'phantomjs', 'selenium', 'webdriver', 'puppeteer',
            'playwright', 'chromedriver', 'geckodriver',
            
            // Generic bot indicators
            'bot', 'crawler', 'spider', 'scraper', 'fetcher', 'parser',
            'monitor', 'checker', 'validator', 'analyzer', 'indexer'
        );
        
        $this->browser_patterns = array(
            'chrome' => '/Chrome\/([0-9\.]+)/i',
            'firefox' => '/Firefox\/([0-9\.]+)/i',
            'safari' => '/Safari\/([0-9\.]+)/i',
            'edge' => '/Edge\/([0-9\.]+)/i',
            'opera' => '/Opera\/([0-9\.]+)/i',
            'internet_explorer' => '/MSIE ([0-9\.]+)/i'
        );
    }
    
    /**
     * Main bot detection method
     */
    public function detect_bot($user_agent, $ip = '', $behavioral_data = array()) {
        $confidence = 0;
        $detection_methods = array();
        
        // User agent analysis
        $ua_result = $this->analyze_user_agent($user_agent);
        $confidence += $ua_result['confidence'];
        if ($ua_result['is_bot']) {
            $detection_methods[] = 'user_agent';
        }
        
        // Behavioral analysis
        if (!empty($behavioral_data)) {
            $behavior_result = $this->analyze_behavior($behavioral_data);
            $confidence += $behavior_result['confidence'];
            if ($behavior_result['is_bot']) {
                $detection_methods[] = 'behavioral';
            }
        }
        
        // IP analysis (if available)
        if (!empty($ip)) {
            $ip_result = $this->analyze_ip($ip);
            $confidence += $ip_result['confidence'];
            if ($ip_result['is_bot']) {
                $detection_methods[] = 'ip_analysis';
            }
        }
        
        // Normalize confidence score
        $confidence = min(1.0, $confidence);
        
        return array(
            'is_bot' => $confidence > 0.5,
            'confidence' => $confidence,
            'detection_methods' => $detection_methods,
            'user_agent_analysis' => $ua_result,
            'behavioral_analysis' => isset($behavior_result) ? $behavior_result : null,
            'ip_analysis' => isset($ip_result) ? $ip_result : null
        );
    }
    
    /**
     * Analyze user agent for bot indicators
     */
    private function analyze_user_agent($user_agent) {
        $user_agent = strtolower($user_agent);
        $confidence = 0;
        $indicators = array();
        
        // Check for bot patterns
        foreach ($this->bot_patterns as $pattern) {
            if (strpos($user_agent, $pattern) !== false) {
                $confidence += 0.8;
                $indicators[] = $pattern;
                break; // One strong indicator is enough
            }
        }
        
        // Check for suspicious characteristics
        if (empty($user_agent) || strlen($user_agent) < 10) {
            $confidence += 0.6;
            $indicators[] = 'empty_or_short_ua';
        }
        
        // Check for missing browser version
        $has_browser = false;
        foreach ($this->browser_patterns as $browser => $pattern) {
            if (preg_match($pattern, $user_agent)) {
                $has_browser = true;
                break;
            }
        }
        
        if (!$has_browser && $confidence < 0.5) {
            $confidence += 0.3;
            $indicators[] = 'no_browser_pattern';
        }
        
        // Check for automation indicators
        $automation_indicators = array('headless', 'automated', 'test', 'script', 'tool');
        foreach ($automation_indicators as $indicator) {
            if (strpos($user_agent, $indicator) !== false) {
                $confidence += 0.4;
                $indicators[] = 'automation_' . $indicator;
            }
        }
        
        return array(
            'is_bot' => $confidence > 0.5,
            'confidence' => min(1.0, $confidence),
            'indicators' => $indicators,
            'browser_detected' => $has_browser
        );
    }
    
    /**
     * Analyze behavioral data for bot indicators
     */
    private function analyze_behavior($data) {
        $confidence = 0;
        $indicators = array();
        
        // Check for webdriver detection
        if (isset($data['webdriver_detected']) && $data['webdriver_detected'] === 'true') {
            $confidence += 0.9;
            $indicators[] = 'webdriver_detected';
        }
        
        // Analyze mouse movements
        $mouse_movements = intval($data['mouse_movements'] ?? 0);
        if ($mouse_movements === 0 && intval($data['time_on_page'] ?? 0) > 5) {
            $confidence += 0.4;
            $indicators[] = 'no_mouse_movement';
        } elseif ($mouse_movements > 0 && $mouse_movements < 5) {
            $confidence += 0.2;
            $indicators[] = 'minimal_mouse_movement';
        }
        
        // Analyze interaction patterns
        $total_interactions = intval($data['click_events'] ?? 0) + 
                             intval($data['keystrokes'] ?? 0) + 
                             intval($data['scroll_events'] ?? 0);
        
        if ($total_interactions === 0 && intval($data['time_on_page'] ?? 0) > 10) {
            $confidence += 0.3;
            $indicators[] = 'no_interactions';
        }
        
        // Check behavior score
        $behavior_score = intval($data['behavior_score'] ?? 0);
        if ($behavior_score < 20) {
            $confidence += 0.3;
            $indicators[] = 'low_behavior_score';
        }
        
        // Check for perfect timing patterns (bot-like)
        $time_on_page = intval($data['time_on_page'] ?? 0);
        if ($time_on_page > 0 && $time_on_page % 1000 === 0) {
            $confidence += 0.2;
            $indicators[] = 'perfect_timing';
        }
        
        // Check plugins count
        $plugins_count = intval($data['plugins_count'] ?? 0);
        if ($plugins_count === 0) {
            $confidence += 0.1;
            $indicators[] = 'no_plugins';
        } elseif ($plugins_count > 50) {
            $confidence += 0.2;
            $indicators[] = 'too_many_plugins';
        }
        
        return array(
            'is_bot' => $confidence > 0.5,
            'confidence' => min(1.0, $confidence),
            'indicators' => $indicators
        );
    }
    
    /**
     * Analyze IP for bot indicators
     */
    private function analyze_ip($ip) {
        $confidence = 0;
        $indicators = array();
        
        // Check for known bot IP ranges (simplified)
        $bot_ip_patterns = array(
            '66.249.', // Google
            '157.55.', // Microsoft
            '40.77.',  // Microsoft
            '207.46.', // Microsoft
            '199.30.', // Facebook
            '173.252.' // Facebook
        );
        
        foreach ($bot_ip_patterns as $pattern) {
            if (strpos($ip, $pattern) === 0) {
                $confidence += 0.7;
                $indicators[] = 'known_bot_ip_range';
                break;
            }
        }
        
        // Check for data center IPs (simplified check)
        if ($this->is_datacenter_ip($ip)) {
            $confidence += 0.3;
            $indicators[] = 'datacenter_ip';
        }
        
        return array(
            'is_bot' => $confidence > 0.5,
            'confidence' => min(1.0, $confidence),
            'indicators' => $indicators
        );
    }
    
    /**
     * Simple datacenter IP detection
     */
    private function is_datacenter_ip($ip) {
        // This is a simplified check. In production, you might want to use
        // a more comprehensive database or API service
        $datacenter_ranges = array(
            '54.', '52.', '34.', '35.', // AWS
            '104.', '168.', '138.', // Google Cloud
            '40.', '52.', '13.', // Azure
            '159.', '169.', '149.', // Oracle Cloud
        );
        
        foreach ($datacenter_ranges as $range) {
            if (strpos($ip, $range) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get browser information (consolidated function)
     */
    public function get_browser_info($user_agent) {
        $browser = 'Unknown';
        $version = '';
        
        foreach ($this->browser_patterns as $name => $pattern) {
            if (preg_match($pattern, $user_agent, $matches)) {
                $browser = ucfirst(str_replace('_', ' ', $name));
                $version = isset($matches[1]) ? $matches[1] : '';
                break;
            }
        }
        
        return array(
            'browser' => $browser,
            'version' => $version,
            'full_name' => $browser . ($version ? ' ' . $version : '')
        );
    }
    
    /**
     * Batch process bot detection for existing data
     */
    public function batch_detect_bots($limit = 100, $offset = 0) {
        global $wpdb;
        
        $database = new Gotham_Database();
        $table_stats = $database->get_table_stats();
        
        $sql = $wpdb->prepare(
            "SELECT id, ip, user_agent, mouse_movements, keystrokes, scroll_events, 
                    click_events, time_on_page, behavior_score, plugins_count, 
                    webdriver_detected FROM {$table_stats} 
             WHERE is_bot = 0 AND bot_confidence = 0 
             ORDER BY id ASC LIMIT %d OFFSET %d",
            $limit, $offset
        );
        
        $results = $wpdb->get_results($sql, ARRAY_A);
        $updated = 0;
        
        foreach ($results as $row) {
            $behavioral_data = array(
                'mouse_movements' => $row['mouse_movements'],
                'keystrokes' => $row['keystrokes'],
                'scroll_events' => $row['scroll_events'],
                'click_events' => $row['click_events'],
                'time_on_page' => $row['time_on_page'],
                'behavior_score' => $row['behavior_score'],
                'plugins_count' => $row['plugins_count'],
                'webdriver_detected' => $row['webdriver_detected']
            );
            
            $detection = $this->detect_bot($row['user_agent'], $row['ip'], $behavioral_data);
            
            $update_result = $wpdb->update(
                $table_stats,
                array(
                    'is_bot' => $detection['is_bot'] ? 1 : 0,
                    'bot_confidence' => $detection['confidence']
                ),
                array('id' => $row['id']),
                array('%d', '%f'),
                array('%d')
            );
            
            if ($update_result !== false) {
                $updated++;
            }
        }
        
        return array(
            'processed' => count($results),
            'updated' => $updated,
            'has_more' => count($results) === $limit
        );
    }
    
    /**
     * Get bot detection statistics
     */
    public function get_bot_stats() {
        global $wpdb;
        
        $database = new Gotham_Database();
        $table_stats = $database->get_table_stats();
        
        $total = $wpdb->get_var("SELECT COUNT(*) FROM {$table_stats}");
        $bots = $wpdb->get_var("SELECT COUNT(*) FROM {$table_stats} WHERE is_bot = 1");
        $humans = $total - $bots;
        
        return array(
            'total_requests' => intval($total),
            'bot_requests' => intval($bots),
            'human_requests' => intval($humans),
            'bot_percentage' => $total > 0 ? round(($bots / $total) * 100, 2) : 0
        );
    }
}
